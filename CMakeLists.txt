cmake_minimum_required(VERSION 3.10)

# 项目名称
project(PsiMdlProject CXX)

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 头文件包含路径
include_directories(./PsiMdl/include)

# 库路径
link_directories(./PsiMdl/libs/linux)

# 目标文件
set(SERVER_TARGET server)
set(CLIENT_TARGET client)

# 源文件
set(SERVER_SRC server.cpp)
set(CLIENT_SRC client.cpp)

# 添加可执行文件
add_executable(${SERVER_TARGET} ${SERVER_SRC})
add_executable(${CLIENT_TARGET} ${CLIENT_SRC})

# 链接库
target_link_libraries(${SERVER_TARGET} mdl_api dl pthread rt json yaml-cpp)
target_link_libraries(${CLIENT_TARGET} mdl_api dl pthread rt json yaml-cpp)

# 设置运行时库路径
set_target_properties(${SERVER_TARGET} PROPERTIES RUNTIME_OUTPUT_DIRECTORY ./)
set_target_properties(${CLIENT_TARGET} PROPERTIES RUNTIME_OUTPUT_DIRECTORY ./)


add_custom_command(
        TARGET ${SERVER_TARGET} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        ${CMAKE_SOURCE_DIR}/config.yaml
        ${CMAKE_BINARY_DIR}/config.yaml
        COMMENT "Copying config.yaml to Myserver directory"
)

add_custom_command(
        TARGET ${CLIENT_TARGET} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        ${CMAKE_SOURCE_DIR}/config.yaml
        ${CMAKE_BINARY_DIR}/config.yaml
        COMMENT "Copying config.yaml to Myclient directory"
)
