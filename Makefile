# 编译器
CXX = g++

# 编译选项
CXXFLAGS = -std=c++17 -O2

# 头文件包含路径
INCS = -I./PsiMdl/include

# 链接库
LDFLAGS = -lmdl_api -ldl -lpthread -lrt -ljson -lyaml-cpp

# 库路径
LIBDIR = -L./PsiMdl/libs/linux
SODIR = -Wl,-rpath=./PsiMdl/libs/linux

# 目标文件
SERVER_TARGET = server
CLIENT_TARGET = client

# 源文件
SERVER_SRC = server.cpp
CLIENT_SRC = client.cpp

# 默认目标
all: $(SERVER_TARGET) $(CLIENT_TARGET)

# 编译 server
$(SERVER_TARGET): $(SERVER_SRC)
	$(CXX) $(CXXFLAGS) $(INCS) -o $(SERVER_TARGET) $(SERVER_SRC) $(LIBDIR) $(LDFLAGS) $(SODIR)

# 编译 client
$(CLIENT_TARGET): $(CLIENT_SRC)
	$(CXX) $(CXXFLAGS) $(INCS) -o $(CLIENT_TARGET) $(CLIENT_SRC) $(LIBDIR) $(LDFLAGS) $(SODIR)

# 清理生成的文件
clean:
	rm -f $(SERVER_TARGET) $(CLIENT_TARGET)
	rm -rf ./Myserver/* ./Myclient/*

.PHONY: all clean