# PSI Trader FangRuiJia 项目学习文档

## 项目概述

这是一个基于TCP和共享内存(SHM)的高性能消息队列框架，专门用于金融交易系统的数据传输。项目实现了客户端-服务端架构，支持持久化消息传输和MDL(Market Data Library)数据订阅。

## 1. 静态分析 - 核心组件详解

### 1.1 命名空间和宏定义

```cpp
#define NS_PSI_TCP_SHM_BEGIN namespace tcpshm{
#define NS_PSI_TCP_SHM_END };
```
- **作用**: 定义项目的命名空间边界，所有核心类都在`tcpshm`命名空间下

### 1.2 核心数据结构

#### MsgHeader (消息头结构体)
**位置**: `PsiHttpShm/PsiMsgHeader.h`

```cpp
struct MsgHeader {
    uint16_t size;        // 消息总大小(包含头部)
    uint16_t msg_type;    // 消息类型(用户设置，不能为0)
    uint32_t ack_seq;     // PTCP内部使用的确认序列号
};
```

**关键方法**:
- `GetMsgBodyPtr<T>()`: 获取消息体指针，类型安全
- `GetMsgBodyRawPtr()`: 获取原始消息体指针
- `ConvertByteOrder<ToLittle>()`: 字节序转换
- `SetMsg<T>(fn)`: 设置消息内容的函数式接口

#### 配置结构体系
**位置**: `PsiHttpShm/PsiTcpShmConf.h`

**CommonConf (基础配置)**:
```cpp
struct CommonConf {
    static constexpr uint32_t NameSize = 16;           // 名称最大长度
    static constexpr bool ToLittleEndian = true;       // 字节序设置
    static constexpr uint32_t ShmQueueSize = 1048576;  // 共享内存队列大小
    
    int64_t NanoInSecond;        // 纳秒转换
    int64_t ConnectionTimeout;   // 连接超时时间
    int64_t HeartBeatInverval;   // 心跳间隔
    uint32_t TcpRecvBufInitSize; // TCP接收缓冲区初始大小
    uint32_t TcpRecvBufMaxSize;  // TCP接收缓冲区最大大小
    bool TcpNoDelay;             // TCP_NODELAY选项
};
```

**ClientConf (客户端配置)**:
```cpp
struct ClientConf : public CommonConf {
    static constexpr uint32_t TcpQueueSize = 2000;
    std::string ClientName;        // 客户端名称
    std::string ServerAddr;        // 服务器地址
    uint16_t ServerPort;           // 服务器端口
    bool UseShm;                   // 是否使用共享内存
    std::string PrefixPTCPFolder;  // PTCP文件目录前缀
};
```

**ServerConf (服务端配置)**:
```cpp
struct ServerConf : public CommonConf {
    std::string ServerName;                              // 服务器名称
    uint16_t ListenPort;                                // 监听端口
    static constexpr uint32_t TcpQueueSize = 1048576;   // TCP队列大小
    uint32_t MaxNewConnections;                         // 最大新连接数
    static constexpr uint32_t MaxShmConnsPerGrp = 4;    // 每组最大共享内存连接数
    uint32_t MaxShmGrps;                                // 最大共享内存组数
    static constexpr uint32_t MaxTcpConnsPerGrp = 4;    // 每组最大TCP连接数
    uint32_t MaxTcpGrps;                                // 最大TCP组数
    uint64_t NewConnectionTimeout;                      // 新连接超时时间
};
```

### 1.3 内存映射工具
**位置**: `PsiHttpShm/PsiMmap.h`

**MyMmap函数**:
```cpp
template<class T>
T* MyMmap(const char* filename, bool use_shm, const char** error_msg, size_t size=sizeof(T))
```
- **功能**: 创建内存映射文件或共享内存
- **参数**:
  - `filename`: 文件名或共享内存名
  - `use_shm`: true使用shm_open，false使用普通文件
  - `error_msg`: 错误信息输出
  - `size`: 映射大小
- **返回**: 映射的内存指针

**MyUnmap函数**:
```cpp
template<class T>
void MyUnmap(void* addr, size_t size=sizeof(T))
```
- **功能**: 解除内存映射

### 1.4 持久化TCP队列
**位置**: `PsiHttpShm/internal/PsiPtcpQueue.h`

**PTCPQueue类**:
```cpp
template<uint32_t Bytes>
class PTCPQueue {
    static const uint32_t BLK_CNT = Bytes / sizeof(MsgHeader);
    // 队列实现细节...
};
```
- **功能**: 实现可持久化的单线程消息队列
- **特点**: 可以映射到文件，支持崩溃恢复

### 1.5 PTCP连接管理
**位置**: `PsiHttpShm/internal/PsiPtcpConn.h`

**PTCPConnection类**:
```cpp
template<class Conf>
class PTCPConnection {
public:
    explicit PTCPConnection(const CommonConf& common_conf);
    bool OpenFile(const char* ptcp_queue_file, const char** error_msg);
    bool GetSeq(uint32_t* local_ack_seq, uint32_t* local_seq_start, uint32_t* local_seq_end);
    void Reset();
    // 更多方法...
};
```

**关键成员变量**:
- `PTCPQ* q_`: 持久化队列指针
- `MsgHeader hbmsg_`: 心跳消息
- `const CommonConf& conf_`: 配置引用

### 1.6 连接抽象层
**位置**: `PsiHttpShm/PsiTcpShmConn.h`

**TcpShmConnection类**:
```cpp
template<class Conf>
class TcpShmConnection {
public:
    // 公共接口
    std::string GetPtcpFile();
    bool IsClosed();
    void Close();
    const char* GetCloseReason(int* sys_errno);
    char* GetRemoteName();
    const char* GetLocalName();
    const char* GetPtcpDir();
    
    // 消息操作
    MsgHeader* Alloc(uint16_t size);
    void Push();
    void PushMore();
    MsgHeader* Front();
    void Pop();
    
    // 模板方法
    template<class T>
    bool TrySendMsg(std::function<void(T*)> fn, bool more = false);
};
```

**关键成员变量**:
- `char remote_name_[CommonConf::NameSize]`: 远程名称
- `char local_name_[CommonConf::NameSize]`: 本地名称
- `const char* ptcp_dir_`: PTCP目录
- `PTCPConnection<Conf> ptcp_conn_`: PTCP连接对象
- `SHMQ* shm_sendq_`: 共享内存发送队列
- `SHMQ* shm_recvq_`: 共享内存接收队列

## 2. 客户端架构分析

### 2.1 TcpShmClient类
**位置**: `PsiHttpShm/PsiTcpShmClient.h`

**类定义**:
```cpp
template<class Derived>
class TcpShmClient {
public:
    using Connection = TcpShmConnection<ClientConf>;
    using LoginMsg = LoginMsgTpl;
    using LoginRspMsg = LoginRspMsgTpl;
    
protected:
    TcpShmClient(const std::string& ptcp_dir, const std::string& client_name, const ClientConf& client_conf);
    virtual ~TcpShmClient();
    
    bool Connect(bool use_shm, const char* server_ipv4, uint16_t server_port, const CommonConf::LoginUserData& login_user_data);
    void PollTcp(int64_t now);
    void PollShm();
    void Stop();
    Connection& GetConnection();
};
```

**关键成员变量**:
- `const ClientConf& conf_`: 客户端配置
- `char client_name_[CommonConf::NameSize]`: 客户端名称
- `char* server_name_`: 服务器名称
- `std::string ptcp_dir_`: PTCP目录
- `Connection conn_`: 连接对象

### 2.2 客户端实现示例
**位置**: `client.cpp`

**Client类继承关系**:
```cpp
class Client : public TcpShmClient<Client> {
    // CRTP模式：Client作为模板参数传递给基类
};
```

**关键方法实现**:
- `Poll()`: 主要的轮询逻辑，发送请求
- `sendPing()`: 发送ping请求
- `sendSub()`: 发送订阅请求
- `handlePing()`: 处理ping响应
- `handleSub()`: 处理订阅响应
- `OnServerMsg()`: 服务器消息分发器

## 3. 服务端架构分析

### 3.1 TcpShmServer类
**位置**: `PsiHttpShm/PsiTcpShmServer.h`

**类定义**:
```cpp
template<class Derived>
class TcpShmServer {
public:
    using Connection = TcpShmConnection<ServerConf>;
    using LoginMsg = LoginMsgTpl;
    using LoginRspMsg = LoginRspMsgTpl;
    
protected:
    bool Start(const char* listen_ipv4, uint16_t listen_port);
    void PollCtl(int64_t now);
    void PollTcp(int64_t now, int grpid);
    void PollShm(int grpid);
    void CustomTcpPoll(std::function<void(Connection&)> func);
    void Stop();
};
```

**连接组管理**:
```cpp
struct ConnectionGroup {
    Connection* conns[MaxConnsPerGrp];
    int live_cnt;
};
```

### 3.2 服务端实现示例
**位置**: `server.cpp`

**Server类关键回调**:
- `OnNewConnection()`: 新连接处理
- `OnClientLogon()`: 客户端登录确认
- `OnClientMsg()`: 客户端消息处理
- `OnClientDisconnected()`: 客户端断开处理

## 4. 消息系统分析

### 4.1 消息类型定义
**位置**: `msg.h`

```cpp
enum MsgType {
    MSG_PING_TYPE = 1,        // Ping消息
    MSG_SUB_TYPE,             // 订阅消息
    MSG_UNSUB_TYPE,           // 取消订阅消息
    MSG_MDL_MESSAGE_TYPE,     // MDL数据消息
};
```

### 4.2 消息模板基类
```cpp
template<uint16_t msg_type_>
struct MsgTpl {
    static const uint16_t msg_type = msg_type_;
};
```

### 4.3 具体消息结构
```cpp
struct PingReq : MsgTpl<MSG_PING_TYPE> {
    char val[10];
};

struct SubReq : MsgTpl<MSG_SUB_TYPE> {
    datayes::mdl::MDLServiceID serviceID;
    int messageID;
};
```

## 5. MDL集成分析

### 5.1 MDL消息处理器
**位置**: `PsiMdl/PsiMdlApi.h`

**MyMessageHandler类**:
- 处理来自MDL的市场数据
- 通过回调函数将数据转发给订阅的客户端
- 支持多种市场数据类型(上海L1、深圳L2等)

### 5.2 数据流转过程
1. MDL推送市场数据到MyMessageHandler
2. MyMessageHandler通过回调函数处理数据
3. 服务端遍历所有连接，找到订阅该数据的客户端
4. 将数据封装成MSG_MDL_MESSAGE_TYPE消息发送

## 6. 类关系图和继承结构

### 6.1 核心类继承关系

```
CommonConf (基础配置)
├── ClientConf (客户端配置)
└── ServerConf (服务端配置)

MsgHeader (消息头)
├── HeartbeatMsg (心跳消息)
└── MsgTpl<T> (消息模板)
    ├── PingReq/PingResp
    ├── SubReq/SubResp
    ├── UnsubReq
    └── LoginMsgTpl/LoginRspMsgTpl

TcpShmConnection<Conf> (连接抽象)
├── 使用 PTCPConnection<Conf> (TCP持久化连接)
├── 使用 PTCPQueue<Bytes> (持久化队列)
└── 使用 MyMmap/MyUnmap (内存映射工具)

TcpShmClient<Derived> (客户端基类)
└── Client (具体客户端实现) - CRTP模式

TcpShmServer<Derived> (服务端基类)
└── Server (具体服务端实现) - CRTP模式

MyMessageHandler (MDL消息处理器)
├── 处理各种MDL消息类型
└── 通过回调函数与Server交互
```

### 6.2 模板参数关系

```cpp
// 客户端模板实例化
TcpShmClient<Client>
├── Connection = TcpShmConnection<ClientConf>
├── LoginMsg = LoginMsgTpl
└── LoginRspMsg = LoginRspMsgTpl

// 服务端模板实例化
TcpShmServer<Server>
├── Connection = TcpShmConnection<ServerConf>
├── LoginMsg = LoginMsgTpl
└── LoginRspMsg = LoginRspMsgTpl

// PTCP连接模板实例化
PTCPConnection<ClientConf>  // 客户端使用
PTCPConnection<ServerConf>  // 服务端使用

// 队列模板实例化
PTCPQueue<ClientConf::TcpQueueSize>   // 客户端队列
PTCPQueue<ServerConf::TcpQueueSize>   // 服务端队列
```

### 6.3 组合关系详解

```cpp
// TcpShmConnection内部组合
class TcpShmConnection<Conf> {
    PTCPConnection<Conf> ptcp_conn_;     // 组合：PTCP连接
    SHMQ* shm_sendq_;                    // 聚合：共享内存发送队列
    SHMQ* shm_recvq_;                    // 聚合：共享内存接收队列
    char remote_name_[NameSize];         // 组合：远程名称
    char local_name_[NameSize];          // 组合：本地名称
};

// TcpShmClient内部组合
class TcpShmClient<Derived> {
    Connection conn_;                     // 组合：连接对象
    const ClientConf& conf_;             // 引用：配置对象
    char client_name_[NameSize];         // 组合：客户端名称
    char* server_name_;                  // 聚合：服务器名称
};

// TcpShmServer内部组合
class TcpShmServer<Derived> {
    ConnectionGroup tcp_grps_[MaxTcpGrps];   // 组合：TCP连接组
    ConnectionGroup shm_grps_[MaxShmGrps];   // 组合：SHM连接组
    const ServerConf& conf_;                 // 引用：配置对象
};
```

## 7. 动态分析 - 调用关系和数据流

### 7.1 客户端启动流程

```
main() 
├── 加载配置文件(config.yaml)
├── 创建Client对象
│   └── TcpShmClient构造函数
│       ├── 初始化配置
│       ├── 设置客户端名称
│       └── 初始化连接对象
└── client.Run()
    ├── Connect() - 连接服务器
    │   ├── 创建socket连接
    │   ├── 发送登录消息
    │   ├── 等待登录响应
    │   └── 建立PTCP/SHM文件
    ├── 创建轮询线程
    │   ├── Poll() - 发送业务请求
    │   └── PollShm() - 处理共享内存消息
    └── 主线程PollTcp() - 处理TCP消息
```

### 7.2 服务端启动流程

```
main()
├── 加载配置文件
├── 创建Server对象
└── server.Run()
    ├── Start() - 启动服务器
    │   ├── 创建监听socket
    │   ├── 绑定端口
    │   └── 开始监听
    ├── 创建TCP轮询线程组
    ├── 创建SHM轮询线程组
    ├── 启动MDL消息处理器
    │   ├── 连接MDL服务器
    │   ├── 设置数据回调函数
    │   └── 开始接收市场数据
    └── 主线程PollCtl() - 处理新连接
```

### 7.3 消息传输流程

#### TCP模式消息流:
```
Client发送:
TrySendMsg() → Alloc() → 填充消息 → Push() → TCP发送

Server接收:
PollTcp() → TcpFront() → OnClientMsg() → 消息处理

Server发送:
TrySendMsg() → Alloc() → 填充响应 → Push() → TCP发送

Client接收:
PollTcp() → TcpFront() → OnServerMsg() → 消息分发
```

#### SHM模式消息流:
```
Client发送:
TrySendMsg() → Alloc() → 填充消息 → Push() → 写入共享内存

Server接收:
PollShm() → ShmFront() → OnClientMsg() → 消息处理

Server发送:
TrySendMsg() → Alloc() → 填充响应 → Push() → 写入共享内存

Client接收:
PollShm() → ShmFront() → OnServerMsg() → 消息分发
```

### 7.4 MDL数据订阅流程

```
1. 客户端发送订阅请求:
   Client.sendSub() → SubReq消息 → 服务端

2. 服务端处理订阅:
   OnClientMsg() → MSG_SUB_TYPE → conn.Subscribe()

3. MDL数据到达:
   MDL推送 → MyMessageHandler回调 → CustomTcpPoll()

4. 数据转发:
   遍历连接 → 检查订阅 → 封装MSG_MDL_MESSAGE_TYPE → 发送

5. 客户端接收:
   OnServerMsg() → MSG_MDL_MESSAGE_TYPE → handleMdlMessage()
```

### 7.5 连接管理流程

#### 连接建立:
```
1. 客户端Connect()
2. 创建TCP socket
3. 发送LoginMsg
4. 服务端OnNewConnection()决定接受/拒绝
5. 建立PTCP/SHM文件映射
6. 序列号同步检查
7. OnClientLogon()确认连接成功
```

#### 连接维护:
```
1. 心跳机制 - 定期发送HeartbeatMsg
2. 序列号确认 - 保证消息可靠传输
3. 错误检测 - 文件损坏、网络异常等
4. 自动重连 - 客户端检测到断开后重新连接
```

#### 连接关闭:
```
1. 正常关闭: Close() → RequestClose() → OnDisconnected()
2. 异常关闭: 网络错误 → 错误检测 → OnDisconnected()
3. 资源清理: 关闭文件描述符 → 解除内存映射 → 释放连接对象
```

## 8. 关键设计模式

### 8.1 CRTP (Curiously Recurring Template Pattern)
- TcpShmClient<Derived>和TcpShmServer<Derived>使用CRTP
- 允许基类调用派生类的方法，实现编译时多态

### 8.2 模板特化
- 针对不同配置类型(ClientConf/ServerConf)特化连接类
- 提供类型安全的消息处理

### 8.3 RAII (Resource Acquisition Is Initialization)
- 内存映射文件的自动管理
- 连接对象的生命周期管理

### 8.4 观察者模式
- MDL数据的订阅/推送机制
- 客户端订阅特定数据类型，服务端推送相应数据

## 9. 性能优化技术

### 9.1 内存管理优化

#### 零拷贝技术
- 使用mmap内存映射避免数据拷贝
- 共享内存直接在进程间传输数据
- 消息头和消息体连续存储，减少内存碎片

#### 内存预分配
```cpp
// 队列大小在编译时确定，避免动态分配
static constexpr uint32_t ShmQueueSize = 1048576;  // 1MB
static constexpr uint32_t TcpQueueSize = 1048576;
```

#### 内存对齐
```cpp
// 保证8字节对齐，提高访问效率
static_assert(Bytes % sizeof(MsgHeader) == 0, "Bytes must be multiple of 8");
```

### 9.2 并发控制

#### 单生产者单消费者(SPSC)模型
- Connection设计为线程不安全，但高效
- 每个连接只能在一个线程中操作
- 避免锁竞争，提高性能

#### 内存屏障
```cpp
// 强制从内存读取，防止编译器优化
asm volatile("" : "=m"(grp.live_cnt) : :);
```

#### 分组管理
- TCP连接分组管理，每组独立轮询
- 共享内存连接分组管理
- 减少锁竞争，提高并发性能

### 9.3 网络优化

#### TCP_NODELAY
```cpp
bool TcpNoDelay;  // 禁用Nagle算法，减少延迟
```

#### 非阻塞I/O
- 使用轮询模式而非阻塞等待
- 避免线程阻塞，提高响应性

#### 批量处理
```cpp
void PushMore();  // 批量发送，减少系统调用
```

## 10. 错误处理机制

### 10.1 连接错误处理

#### 文件错误
```cpp
void OnClientFileError(Connection& conn, const char* reason, int sys_errno);
```
- PTCP文件损坏检测
- 共享内存文件访问错误
- 自动错误恢复机制

#### 序列号不匹配
```cpp
void OnSeqNumberMismatch(Connection& conn, uint32_t local_ack_seq, ...);
```
- 检测消息丢失或重复
- 提供手动修复接口
- 保证消息传输可靠性

#### 网络错误
```cpp
void OnSystemError(const char* error_msg, int sys_errno);
void OnDisconnected(const char* reason, int sys_errno);
```
- 网络连接异常处理
- 自动重连机制
- 错误日志记录

### 10.2 消息错误处理

#### 消息格式验证
- 消息大小检查
- 消息类型验证
- 序列号连续性检查

#### 超时处理
```cpp
int64_t ConnectionTimeout;    // 连接超时
int64_t HeartBeatInverval;   // 心跳超时
uint64_t NewConnectionTimeout; // 新连接超时
```

## 11. 配置系统详解

### 11.1 YAML配置文件结构
```yaml
TcpShm:
  CommonConf:
    NanoInSecond: 1000000000
    ConnectionTimeout: 10000000000
    HeartBeatInverval: 5000000000
  ClientConf:
    ClientName: "TestClient"
    ServerAddr: "127.0.0.1"
    ServerPort: 12345
    UseShm: true
    PrefixPTCPFolder: "/tmp/ptcp/"
    TcpRecvBufInitSize: 8192
    TcpRecvBufMaxSize: 65536
    TcpNoDelay: true
  ServerConf:
    ServerName: "TestServer"
    ListenPort: 12345
    MaxNewConnections: 100
    MaxShmGrps: 4
    MaxTcpGrps: 4
    NewConnectionTimeout: 30000000000

MdlApi:
  ServerAddr: "127.0.0.1"
  ServerPort: "9001"
  # MDL相关配置...
```

### 11.2 配置解析函数
```cpp
template<typename T>
void parseCommonConf(const YAML::Node& node, T& conf);
void parseClientConf(const YAML::Node& node, ClientConf& conf);
void parseServerConf(const YAML::Node& node, ServerConf& conf);
```

## 12. 调试和监控

### 12.1 日志系统
- 使用cout进行简单日志输出
- 关键事件记录：连接建立、消息收发、错误发生
- 性能指标监控：消息处理速度、连接数量

### 12.2 状态查询接口
```cpp
bool IsClosed();                    // 连接状态
const char* GetCloseReason();       // 关闭原因
std::string GetPtcpFile();         // PTCP文件路径
char* GetRemoteName();              // 远程名称
```

### 12.3 调试工具
- PTCP文件内容检查
- 共享内存状态监控
- 序列号一致性验证

## 13. 扩展和定制

### 13.1 自定义消息类型
```cpp
// 1. 在msg.h中定义新的消息类型
enum MsgType {
    MSG_CUSTOM_TYPE = 100,  // 自定义消息类型
};

// 2. 定义消息结构
struct CustomReq : MsgTpl<MSG_CUSTOM_TYPE> {
    int custom_field;
};

// 3. 在客户端/服务端添加处理逻辑
case MSG_CUSTOM_TYPE: {
    handleCustom(header->GetMsgBodyPtr<CustomReq>());
    break;
}
```

### 13.2 自定义配置
```cpp
// 继承CommonConf添加自定义配置项
struct MyCustomConf : public CommonConf {
    std::string CustomField;
    int CustomValue;
};
```

### 13.3 自定义回调
```cpp
// 重写基类的虚函数实现自定义行为
class MyClient : public TcpShmClient<MyClient> {
    void OnLoginSuccess(const LoginRspMsg* login_rsp) override {
        // 自定义登录成功处理
    }
};
```

## 14. 最佳实践

### 14.1 性能优化建议
1. **合理设置队列大小**: 根据消息频率和大小调整TcpQueueSize和ShmQueueSize
2. **选择合适的传输方式**: 本地通信优先使用SHM，远程通信使用TCP
3. **批量处理消息**: 使用PushMore()进行批量发送
4. **避免频繁内存分配**: 重用消息对象，预分配缓冲区

### 14.2 可靠性保证
1. **序列号管理**: 定期检查序列号一致性
2. **心跳机制**: 合理设置心跳间隔，及时检测连接状态
3. **错误恢复**: 实现自动重连和状态恢复机制
4. **数据持久化**: 利用PTCP文件实现消息持久化

### 14.3 开发建议
1. **模块化设计**: 将业务逻辑与通信逻辑分离
2. **错误处理**: 完善错误处理和日志记录
3. **测试覆盖**: 编写单元测试和集成测试
4. **文档维护**: 及时更新API文档和使用说明

## 15. 总结

PSI Trader FangRuiJia项目是一个高性能的金融数据传输框架，具有以下特点：

**技术优势**:
- 双模式传输(TCP/SHM)支持不同场景需求
- 持久化消息队列保证数据可靠性
- 高性能设计适合低延迟交易系统
- 模块化架构便于扩展和维护

**应用场景**:
- 高频交易系统的数据传输
- 市场数据分发和订阅
- 金融系统间的可靠通信
- 实时数据处理和分析

**学习价值**:
- 深入理解高性能网络编程
- 学习系统级编程和内存管理
- 掌握金融系统的架构设计
- 了解现代C++的最佳实践

这个项目为学习高性能系统设计、网络编程和金融技术提供了优秀的实例，值得深入研究和实践。
