#pragma once
#include <sys/shm.h>
#include <sys/stat.h>
#include <sys/mman.h>
#include <unistd.h>
#include <fcntl.h>
template<class T>
T* MyMmap(const char* filename, bool use_shm, const char** error_msg,size_t size=sizeof(T)) {
    int fd = -1;
    if(use_shm) {
        fd = shm_open(filename, O_CREAT | O_RDWR, 0666);
    }
    else {
        fd = open(filename, O_CREAT | O_RDWR, 0644);
    }
    if(fd == -1) {
        *error_msg = "open";
        return nullptr;
    }
    if(ftruncate(fd, sizeof(T))) {
        *error_msg = "ftruncate";
        close(fd);
        return nullptr;
    }
    T* ret = (T*)mmap(NULL, size, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0);//坑！
    close(fd);
    if(ret == MAP_FAILED) {
        *error_msg = "mmap";
        return nullptr;
    }
    return ret;
}

template<class T>
void MyUnmap(void* addr,size_t size=sizeof(T)) {
    munmap(addr, size);
}