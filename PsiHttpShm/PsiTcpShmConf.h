#pragma once
#include <cstdint>
#include <ctime>
#include <yaml-cpp/yaml.h>
#define  NS_PSI_TCP_SHM_BEGIN  namespace tcpshm{
#define  NS_PSI_TCP_SHM_END };

NS_PSI_TCP_SHM_BEGIN
struct CommonConf
{
    //这里不太好修改…涉及到申请固定大小内存
    static constexpr uint32_t NameSize = 16;
    static constexpr bool ToLittleEndian = true; // set to the endian of majority of the hosts
    static constexpr uint32_t ShmQueueSize=1048576; // must be power of 2

    int64_t NanoInSecond;
    int64_t ConnectionTimeout;
    int64_t HeartBeatInverval;
    // 全局设置 ↑ 客户端/服务端共用 ↓

    uint32_t TcpRecvBufInitSize; // must be a multiple of 8
    uint32_t TcpRecvBufMaxSize;  // must be a multiple of 8
    bool TcpNoDelay;

    using LoginUserData = char;
    using LoginRspUserData = char;
    using ConnectionUserData = char;
};

struct ClientConf : public CommonConf {
    static constexpr uint32_t TcpQueueSize=2000;
    std::string ClientName;
    std::string ServerAddr;
    uint16_t ServerPort;
    bool UseShm;
    std::string PrefixPTCPFolder;
};

struct ServerConf : public CommonConf
{
    std::string ServerName;
    uint16_t ListenPort;

    static constexpr uint32_t TcpQueueSize=1048576;//8的倍数
    uint32_t MaxNewConnections;
    static constexpr uint32_t MaxShmConnsPerGrp = 4;
    uint32_t MaxShmGrps;
    static constexpr uint32_t MaxTcpConnsPerGrp = 4;
    uint32_t MaxTcpGrps;

    uint64_t NewConnectionTimeout;
};

template<typename T>
void parseCommonConf(const YAML::Node& node, T& conf)
{
    const YAML::Node& common_node = node["CommonConf"];
    conf.NanoInSecond = common_node["NanoInSecond"].as<int64_t>();
    conf.ConnectionTimeout = common_node["ConnectionTimeout"].as<int64_t>();
    conf.HeartBeatInverval = common_node["HeartBeatInverval"].as<int64_t>();
}

inline void parseClientConf(const YAML::Node& node, ClientConf& conf)
{
    try {
        parseCommonConf(node, conf);
        const YAML::Node& client_node = node["ClientConf"];
        conf.ClientName = client_node["ClientName"].as<std::string>();
        conf.ServerAddr = client_node["ServerAddr"].as<std::string>();
        conf.ServerPort = client_node["ServerPort"].as<uint16_t>();
        conf.UseShm = client_node["UseShm"].as<bool>();
        conf.PrefixPTCPFolder = client_node["PrefixPTCPFolder"].as<std::string>();

        conf.TcpRecvBufInitSize = client_node["TcpRecvBufInitSize"].as<uint32_t>();
        conf.TcpRecvBufMaxSize = client_node["TcpRecvBufMaxSize"].as<uint32_t>();
        conf.TcpNoDelay = client_node["TcpNoDelay"].as<bool>();
    }
    catch (const YAML::Exception& e)
    {
        std::cerr << "YAML error: " << e.what() << std::endl;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Error: " << e.what() << std::endl;
    }
}

inline void parseServerConf(const YAML::Node& node, ServerConf& conf)
{
    try {
        parseCommonConf(node, conf);
        const YAML::Node& server_node = node["ServerConf"];
        conf.ServerName = server_node["ServerName"].as<std::string>();
        conf.ListenPort = server_node["ListenPort"].as<uint16_t>();

        conf.MaxNewConnections = server_node["MaxNewConnections"].as<uint32_t>();
        conf.MaxShmGrps = server_node["MaxShmGrps"].as<uint32_t>();
        conf.MaxTcpGrps = server_node["MaxTcpGrps"].as<uint32_t>();

        conf.TcpRecvBufInitSize = server_node["TcpRecvBufInitSize"].as<uint32_t>();
        conf.TcpRecvBufMaxSize = server_node["TcpRecvBufMaxSize"].as<uint32_t>();
        conf.TcpNoDelay = server_node["TcpNoDelay"].as<bool>();

        conf.NewConnectionTimeout = server_node["NewConnectionTimeout"].as<uint64_t>();
    }
    catch (const YAML::Exception& e)
    {
        std::cerr << "YAML error: " << e.what() << std::endl;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Error: " << e.what() << std::endl;
    }
}

template<uint16_t MsgType>
struct MsgTpl
{
    static const uint16_t msg_type = MsgType;
};

inline unsigned long long now() {
  timespec ts;
  ::clock_gettime(CLOCK_REALTIME, &ts);
  return ts.tv_sec * 1000000000 + ts.tv_nsec;
}
NS_PSI_TCP_SHM_END