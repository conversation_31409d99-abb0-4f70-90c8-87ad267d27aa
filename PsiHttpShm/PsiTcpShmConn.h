#pragma once
#include<string>
#include <sys/socket.h>
#include "./PsiMsgHeader.h"
#include "PsiTcpShmConf.h"
#include "./internal/PsiPtcpConn.h"
#include "./internal/PsiSpscVarQ.h"
#define  NS_PSI_TCP_SHM_BEGIN  namespace tcpshm{
#define  NS_PSI_TCP_SHM_END };

NS_PSI_TCP_SHM_BEGIN
    template<class Conf>
    class TcpShmConnection {
    public:
        /**
         * 获取 PTCP 文件的路径
         * @return PTCP 文件的路径
         */
        std::string GetPtcpFile();

        /**
         * 判断连接是否已关闭
         * @return 连接已关闭返回 true，否则返回 false
         */
        bool IsClosed();

        /**
         * 关闭连接
         */
        void Close();

        /**
         * 获取连接关闭原因
         * @param sys_errno 存储系统错误号的指针
         * @return 关闭原因字符串
         */
        const char *GetCloseReason(int *sys_errno);

        /**
         * 获取远程名称
         * @return 远程名称字符串
         */
        char *GetRemoteName();

        /**
         * 获取本地名称
         * @return 本地名称字符串
         */
        const char *GetLocalName();

        /**
         * 获取 PTCP 目录
         * @return PTCP 目录字符串
         */
        const char *GetPtcpDir();

        /**
         * 分配一个指定大小的消息
         * @param size 消息大小
         * @return 分配的消息头指针，如果空间不足则返回 nullptr
         */
        MsgHeader *Alloc(uint16_t size);

        /**
         * 提交最后一个消息来自Alloc()并发送
         */
        void Push();

        /**
         * 提交更多消息
            *   对于shm,和Push相同
            *   对于tcp,不立即发送，因为我们还有更多消息要推送
         */
        void PushMore();

        /**
         *
         * 堆上分配缓冲区，用于send
         * @param size
         * @return
         */
        MsgHeader *AllocRaw(uint16_t size);
        /**
         * 发送消息
         * @param header 消息头指针
         * @return 发送成功返回 true，否则返回 false
         */
        bool PushRaw(MsgHeader* header);

        /**
         * 获取下一个消息
         * @return 下一个消息头指针，如果队列为空则返回 nullptr，返回地址保证8字节对齐
         */
        MsgHeader *Front();

        /**
         * 消费从Front()或者polling函数获取的消息
         */
        void Pop();

        /**
        * 单纯发送一条信息(可以是客户端也可以是服务端
        * 无回调函数则默认什么也不做，即消息已经被构建
        */
        template<typename T>
        bool TrySendMsg(std::function<void(T *)> fn,bool is_client=true);

 ////////////////////订阅专用///////////////////
        /**
         * 订阅
         */
        void Subscribe(const int topic) {
            subscriptions_[topic] = true;
        }

        /**
       * 取消订阅某个主题
       */
        void Unsubscribe(const int topic) {
            subscriptions_.erase(topic);
        }

        /**
       * 检测是否订阅了某个主题
       */
        bool IsSubscribed(const int topic) const {
            return subscriptions_.find(topic) != subscriptions_.end();
        }
/////////////////////////////////////////////////////
        CommonConf::ConnectionUserData user_data{};

    private:
        template<class T1>
        friend class TcpShmClient;
        template<class T1>
        friend class TcpShmServer;

        TcpShmConnection(const CommonConf &common_conf);

        /**
         * 初始化 TcpShmConnection 对象
         * @param ptcp_dir PTCP 目录
         * @param local_name 本地名称
         */
        void init(const char *ptcp_dir, const char *local_name);

        /**
         * 打开文件
         * @param use_shm 是否使用共享内存
         * @param error_msg 存储错误信息的指针
         * @return 打开成功返回 true，否则返回 false
         */
        bool OpenFile(bool use_shm, const char **error_msg);

        /**
         * 获取序列号
         * @param local_ack_seq 本地确认序列号
         * @param local_seq_start 本地序列号开始
         * @param local_seq_end 本地序列号结束
         * @param error_msg 存储错误信息的指针
         * @return 获取成功返回 true，否则返回 false
         */
        bool GetSeq(uint32_t *local_ack_seq, uint32_t *local_seq_start, uint32_t *local_seq_end,
                    const char **error_msg);

        /**
         * 重置连接
         */
        void Reset();

        /**
         * 释放连接
         */
        void Release();

        /**
         * 打开套接字
         * @param sock_fd 套接字文件描述符
         * @param remote_ack_seq 远程确认序列号
         * @param now 当前时间
         */
        void Open(int sock_fd, uint32_t remote_ack_seq, int64_t now);

        /**
         * 尝试关闭套接字
         * @return 关闭成功返回 true，否则返回 false
         */
        bool TryCloseFd();

        /**
         * 获取 TCP 消息头
         * @param now 当前时间
         * @return TCP 消息头指针，如果队列为空则返回 nullptr
         */
        MsgHeader *TcpFront(int64_t now);

        /**
         * 获取共享内存消息头
         * @return 共享内存消息头指针，如果队列为空则返回 nullptr
         */
        MsgHeader *ShmFront();

    private:
        const CommonConf &conf_;

        const char *local_name_;
        char remote_name_[CommonConf::NameSize];
        const char *ptcp_dir_ = nullptr;
        PTCPConnection<Conf> ptcp_conn_;
        using SHMQ = SPSCVarQueue<CommonConf::ShmQueueSize>;
        alignas(64) SHMQ *shm_sendq_ = nullptr;
        SHMQ *shm_recvq_ = nullptr;

        std::unordered_map<int, bool> subscriptions_;
    };

NS_PSI_TCP_SHM_END

#include "./PsiTcpShmConn.tpp"
