
#pragma once
#include <string>
#include <netinet/in.h>
#include "./PsiTcpShmConn.h"
#include "PsiTcpShmConf.h"
#include "./internal/PsiPtcpConn.h"
#define  NS_PSI_TCP_SHM_BEGIN  namespace tcpshm{
#define  NS_PSI_TCP_SHM_END };

NS_PSI_TCP_SHM_BEGIN
template<class Derived>
class TcpShmServer
{
public:
    using Connection = TcpShmConnection<ServerConf>;
    using LoginMsg = LoginMsgTpl;
    using LoginRspMsg = LoginRspMsgTpl;

protected:
    /**
     * TcpShmServer类构造函数
     * @param server_name 服务器名称
     * @param ptcp_dir    TCP目录
     * @param conf        服务器配置
     */
    TcpShmServer( const std::string& ptcp_dir,const std::string& server_name,const ServerConf& server_conf);

    /**
     * TcpShmServer类析构函数
     */
    virtual ~TcpShmServer();

    /**
     * 启动服务器
     * @param listen_ipv4 监听的IPv4地址
     * @param listen_port 监听的端口号
     * @return 成功返回true，否则返回false
     */
    bool Start(const char* listen_ipv4, uint16_t listen_port);

    /**
     * 轮询控制，处理新连接并保持共享内存连接活跃
     * @param now 当前时间戳
     */
    void PollCtl(int64_t now);

    /**
     * 轮询TCP，处理TCP连接
     * @param now 当前时间戳
     * @param grpid 组ID
     */
    void PollTcp(int64_t now, int grpid);

    /**
     * 自定义的轮询所有Connection的事件
     * @param func 自定义的回调函数
     */
    void CustomTcpPoll(std::function<void(Connection&)> func);
    /**
     * 轮询共享内存，处理共享内存连接
     * @param grpid 组ID
     */
    void PollShm(int grpid);

    // /**
    //  * 判断是否有对应连接，C++不能返回一个空对象的引用，可以用指针来
    //  * @param client_name
    //  */
    // bool HasConn(const char* client_name);
    /**
     * 通过客户端名称，找到对应连接
     * @param client_name
     */
    Connection* GetConnOrNull(const char* client_name);
    /**
     * 停止服务器
     */
    void Stop();

private:
    struct NewConn
    {
        int64_t time;
        int fd = -1;
        struct sockaddr_in addr{};
        MsgHeader recvbuf[1 + (sizeof(LoginMsg) + 7) / 8];
    };
    template<uint32_t N>
    struct alignas(64) ConnectionGroup
    {
        uint32_t live_cnt = 0;
        Connection* conns[N];
    };

    template<uint32_t N>
    void HandleLogin(int64_t now, NewConn& conn, ConnectionGroup<N>* grps);

    // 检查是否 seq_start <= ack_seq <= seq_end，考虑 uint32_t 的溢出
    bool CheckAckInQueue(uint32_t ack_seq, uint32_t seq_start, uint32_t seq_end);

private:
    const ServerConf& conf_;

    char server_name_[ServerConf::NameSize]{};
    std::string ptcp_dir_;
    int listenfd_ = -1;

    std::vector<NewConn> new_conns_;
    int avail_idx_ = 0;

    std::vector<Connection> conn_pool_;
    std::vector<ConnectionGroup<ServerConf::MaxShmConnsPerGrp>> shm_grps_;
    std::vector<ConnectionGroup<ServerConf::MaxTcpConnsPerGrp>> tcp_grps_;
};
NS_PSI_TCP_SHM_END
#include "./PsiTcpShmServer.tpp"
