#pragma once
#include <cstdio>
#include "./include/mdl_api.h"
#include "./include/mdl_api_msg.h"
#include "./include/mdl_api_types.h"
#include "./include/mdl_bar_msg.h"
#include "./include/mdl_cffexl2_msg.h"
#include "./include/mdl_cffex_msg.h"
#include "./include/mdl_cni_msg.h"
#include "./include/mdl_csi_msg.h"
#include "./include/mdl_czcel2_msg.h"
#include "./include/mdl_czce_msg.h"
#include "./include/mdl_dcel2_msg.h"
#include "./include/mdl_dce_msg.h"
#include "./include/mdl_digicur_msg.h"
#include "./include/mdl_gfexl2_msg.h"
#include "./include/mdl_gfex_msg.h"
#include "./include/mdl_hkex_msg.h"
#include "./include/mdl_neeq_msg.h"
#include "./include/mdl_shfel2_msg.h"
#include "./include/mdl_shfe_msg.h"
#include "./include/mdl_shl1_msg.h"
#include "./include/mdl_shl2_msg.h"
#include "./include/mdl_shny_msg.h"
#include "./include/mdl_swg_msg.h"
#include "./include/mdl_sys_msg.h"
#include "./include/mdl_szl1_msg.h"
#include "./include/mdl_szl2_msg.h"
#include "./PsiMdlApiConf.h"
#include "../msg.h"
#include <utility>
#include <vector>

using namespace datayes;
using namespace datayes::mdl;


// 定义每个市场的结构体
struct MDLSHL1Msg {
    std::vector<RefCountedPtrT<MDLMessage> > shl1Index;
    std::vector<RefCountedPtrT<MDLMessage> > shl1Stock;
    std::vector<RefCountedPtrT<MDLMessage> > indexes;
    std::vector<RefCountedPtrT<MDLMessage> > equity;
    std::vector<RefCountedPtrT<MDLMessage> > bonds;
    std::vector<RefCountedPtrT<MDLMessage> > funds;
    std::vector<RefCountedPtrT<MDLMessage> > level1Plus;
    std::vector<RefCountedPtrT<MDLMessage> > equity2;
    std::vector<RefCountedPtrT<MDLMessage> > bond2;
    std::vector<RefCountedPtrT<MDLMessage> > fund2;
    std::vector<RefCountedPtrT<MDLMessage> > comp;
    std::vector<RefCountedPtrT<MDLMessage> > bondDist;
    std::vector<RefCountedPtrT<MDLMessage> > atp;
} sh1_msg;

struct MDLSHL2Msg {
    std::vector<RefCountedPtrT<MDLMessage> > shl1Stock;
    std::vector<RefCountedPtrT<MDLMessage> > shl2Transaction;
    std::vector<RefCountedPtrT<MDLMessage> > shl2MarketData;
    std::vector<RefCountedPtrT<MDLMessage> > shl2VirtualAuctionPrice;
    std::vector<RefCountedPtrT<MDLMessage> > shl2Index;
    std::vector<RefCountedPtrT<MDLMessage> > shl2MarketOverview;
    std::vector<RefCountedPtrT<MDLMessage> > shl2Statics;
    std::vector<RefCountedPtrT<MDLMessage> > optLevel1;
    std::vector<RefCountedPtrT<MDLMessage> > mdSnapshotFullRefresh_4001;
    std::vector<RefCountedPtrT<MDLMessage> > fixedIncomePrice;
    std::vector<RefCountedPtrT<MDLMessage> > fixedIncomeInfo;
    std::vector<RefCountedPtrT<MDLMessage> > fixedIncomeTransaction;
    std::vector<RefCountedPtrT<MDLMessage> > fixedIncomeFirmQuote;
    std::vector<RefCountedPtrT<MDLMessage> > realQuota;
    std::vector<RefCountedPtrT<MDLMessage> > atpMarketData;
    std::vector<RefCountedPtrT<MDLMessage> > atpTransaction;
    std::vector<RefCountedPtrT<MDLMessage> > shl2Transaction2;
    std::vector<RefCountedPtrT<MDLMessage> > order;
    std::vector<RefCountedPtrT<MDLMessage> > bondMarketData;
    std::vector<RefCountedPtrT<MDLMessage> > bondTick;
    std::vector<RefCountedPtrT<MDLMessage> > bondOrderQueue;
    std::vector<RefCountedPtrT<MDLMessage> > marketInfo;
    std::vector<RefCountedPtrT<MDLMessage> > ngtsTick;
    std::vector<RefCountedPtrT<MDLMessage> > etfMarketData;
    std::vector<RefCountedPtrT<MDLMessage> > bondDist;
    std::vector<RefCountedPtrT<MDLMessage> > bondDistOrderQueue;
    std::vector<RefCountedPtrT<MDLMessage> > bondDistTick;
} sh2_msg;

struct MDLSZL1Msg {
    std::vector<RefCountedPtrT<MDLMessage> > szl1Index;
    std::vector<RefCountedPtrT<MDLMessage> > szl1Stock;
    std::vector<RefCountedPtrT<MDLMessage> > level1Plus;
    std::vector<RefCountedPtrT<MDLMessage> > szl1Option;
    std::vector<RefCountedPtrT<MDLMessage> > szl1Option2;
    std::vector<RefCountedPtrT<MDLMessage> > bonds;
    std::vector<RefCountedPtrT<MDLMessage> > order300392;
    std::vector<RefCountedPtrT<MDLMessage> > transaction300391;
    std::vector<RefCountedPtrT<MDLMessage> > bondDistribution;
    std::vector<RefCountedPtrT<MDLMessage> > atp;
    std::vector<RefCountedPtrT<MDLMessage> > index2;
    std::vector<RefCountedPtrT<MDLMessage> > snapshot309211;
} sz1_msg;

struct MDLSZL2Msg {
    std::vector<RefCountedPtrT<MDLMessage> > trade;
    std::vector<RefCountedPtrT<MDLMessage> > order;
    std::vector<RefCountedPtrT<MDLMessage> > index;
    std::vector<RefCountedPtrT<MDLMessage> > marketData;
    std::vector<RefCountedPtrT<MDLMessage> > stockStatus;
    std::vector<RefCountedPtrT<MDLMessage> > stockInfo;
    std::vector<RefCountedPtrT<MDLMessage> > sysParam;
    std::vector<RefCountedPtrT<MDLMessage> > snapshot300111;
    std::vector<RefCountedPtrT<MDLMessage> > snapshot309011;
    std::vector<RefCountedPtrT<MDLMessage> > snapshot309111;
    std::vector<RefCountedPtrT<MDLMessage> > snapshot300611;
    std::vector<RefCountedPtrT<MDLMessage> > securityStatus;
    std::vector<RefCountedPtrT<MDLMessage> > order300192;
    std::vector<RefCountedPtrT<MDLMessage> > order300592;
    std::vector<RefCountedPtrT<MDLMessage> > order300792;
    std::vector<RefCountedPtrT<MDLMessage> > transaction300191;
    std::vector<RefCountedPtrT<MDLMessage> > transaction300591;
    std::vector<RefCountedPtrT<MDLMessage> > transaction300791;
    std::vector<RefCountedPtrT<MDLMessage> > bulletin;
    std::vector<RefCountedPtrT<MDLMessage> > snapshot300111_v2;
    std::vector<RefCountedPtrT<MDLMessage> > snapshot309011_v2;
    std::vector<RefCountedPtrT<MDLMessage> > snapshot309111_v2;
    std::vector<RefCountedPtrT<MDLMessage> > snapshot300611_v2;
    std::vector<RefCountedPtrT<MDLMessage> > order300192_v2;
    std::vector<RefCountedPtrT<MDLMessage> > order300592_v2;
    std::vector<RefCountedPtrT<MDLMessage> > order300792_v2;
    std::vector<RefCountedPtrT<MDLMessage> > transaction300191_v2;
    std::vector<RefCountedPtrT<MDLMessage> > transaction300591_v2;
    std::vector<RefCountedPtrT<MDLMessage> > transaction300791_v2;
    std::vector<RefCountedPtrT<MDLMessage> > snapshot300111_v3;
    std::vector<RefCountedPtrT<MDLMessage> > realQuota;
    std::vector<RefCountedPtrT<MDLMessage> > snapshot306311;
    std::vector<RefCountedPtrT<MDLMessage> > snapshot300211;
    std::vector<RefCountedPtrT<MDLMessage> > order300292;
    std::vector<RefCountedPtrT<MDLMessage> > transaction300291;
    std::vector<RefCountedPtrT<MDLMessage> > order300392;
    std::vector<RefCountedPtrT<MDLMessage> > transaction300391;
    std::vector<RefCountedPtrT<MDLMessage> > bondOrderQueue;
    std::vector<RefCountedPtrT<MDLMessage> > bondDistribution;
    std::vector<RefCountedPtrT<MDLMessage> > snapshot309211;
    std::vector<RefCountedPtrT<MDLMessage> > etfData;
    std::vector<RefCountedPtrT<MDLMessage> > marketInfo;
} sz2_msg;

struct MDLDCEMsg {
    std::vector<RefCountedPtrT<MDLMessage> > ctpFuture;
    std::vector<RefCountedPtrT<MDLMessage> > future;
    std::vector<RefCountedPtrT<MDLMessage> > futurePlus;
    std::vector<RefCountedPtrT<MDLMessage> > ctpOption;
    std::vector<RefCountedPtrT<MDLMessage> > indexes;
    std::vector<RefCountedPtrT<MDLMessage> > index2;
} dce_msg;

struct MDLDCEL2Msg {
    std::vector<RefCountedPtrT<MDLMessage> > future;
    std::vector<RefCountedPtrT<MDLMessage> > option;
    std::vector<RefCountedPtrT<MDLMessage> > futureTrdQty;
    std::vector<RefCountedPtrT<MDLMessage> > optionTrdQty;
    std::vector<RefCountedPtrT<MDLMessage> > cmbTypeInfo;
    std::vector<RefCountedPtrT<MDLMessage> > optionParam;
    std::vector<RefCountedPtrT<MDLMessage> > futureOrder;
    std::vector<RefCountedPtrT<MDLMessage> > optionOrder;
    std::vector<RefCountedPtrT<MDLMessage> > cmbOrder;
    std::vector<RefCountedPtrT<MDLMessage> > orderStat;
} dcel2_msg;

struct MDLSHFEMsg {
    std::vector<RefCountedPtrT<MDLMessage> > ctpFuture;
    std::vector<RefCountedPtrT<MDLMessage> > future;
    std::vector<RefCountedPtrT<MDLMessage> > futurePlus;
    std::vector<RefCountedPtrT<MDLMessage> > ctpOption;
    std::vector<RefCountedPtrT<MDLMessage> > index;
} shfe_msg;

struct MDLSHFEL2Msg {
    std::vector<RefCountedPtrT<MDLMessage> > ctpFuture;
    std::vector<RefCountedPtrT<MDLMessage> > ctpOption;
    std::vector<RefCountedPtrT<MDLMessage> > crudeFuture;
    std::vector<RefCountedPtrT<MDLMessage> > crudeOption;
} shfel2_msg;

struct MDLCZCEMsg {
    std::vector<RefCountedPtrT<MDLMessage> > ctpFuture;
    std::vector<RefCountedPtrT<MDLMessage> > future;
    std::vector<RefCountedPtrT<MDLMessage> > czceFuture;
    std::vector<RefCountedPtrT<MDLMessage> > level2Future;
    std::vector<RefCountedPtrT<MDLMessage> > futurePlus;
    std::vector<RefCountedPtrT<MDLMessage> > ctpOption;
} czce_msg;

struct MDLCZCEL2Msg {
    std::vector<RefCountedPtrT<MDLMessage> > ctpFuture;
    std::vector<RefCountedPtrT<MDLMessage> > ctpOption;
    std::vector<RefCountedPtrT<MDLMessage> > futureTrdQtyStatus;
    std::vector<RefCountedPtrT<MDLMessage> > optionTrdQtyStatus;
    std::vector<RefCountedPtrT<MDLMessage> > cmbTypeInfo;
    std::vector<RefCountedPtrT<MDLMessage> > optionParam;
    std::vector<RefCountedPtrT<MDLMessage> > tradeStatus;
} czcel2_msg;

struct MDLCFFEXMsg {
    std::vector<RefCountedPtrT<MDLMessage> > ctpFuture;
    std::vector<RefCountedPtrT<MDLMessage> > future;
    std::vector<RefCountedPtrT<MDLMessage> > futurePlus;
    std::vector<RefCountedPtrT<MDLMessage> > option;
} cffex_msg;

struct MDLCFFEXL2Msg {
    std::vector<RefCountedPtrT<MDLMessage> > future;
    std::vector<RefCountedPtrT<MDLMessage> > option;
} cffexl2_msg;

struct MDLHKEXMsg {
    std::vector<RefCountedPtrT<MDLMessage> > omdMarketData;
    std::vector<RefCountedPtrT<MDLMessage> > hangSengIndex;
    std::vector<RefCountedPtrT<MDLMessage> > omdClosingPrice;
    std::vector<RefCountedPtrT<MDLMessage> > scMarketTurnover;
    std::vector<RefCountedPtrT<MDLMessage> > scDailyQuotaBalance;
    std::vector<RefCountedPtrT<MDLMessage> > omdIndexData;
    std::vector<RefCountedPtrT<MDLMessage> > omdSecurityDefinition;
    std::vector<RefCountedPtrT<MDLMessage> > omdTradeTicker;
    std::vector<RefCountedPtrT<MDLMessage> > brokerQueue;
    std::vector<RefCountedPtrT<MDLMessage> > omdOrderBook;
} hkex_msg;

struct MDLGFEXMsg {
    std::vector<RefCountedPtrT<MDLMessage> > future;
    std::vector<RefCountedPtrT<MDLMessage> > option;
    std::vector<RefCountedPtrT<MDLMessage> > futurePlus;
    std::vector<RefCountedPtrT<MDLMessage> > cmbTypeInfo;
} gfex_msg;

struct MDLGFEXL2Msg {
    std::vector<RefCountedPtrT<MDLMessage> > future;
    std::vector<RefCountedPtrT<MDLMessage> > option;
    std::vector<RefCountedPtrT<MDLMessage> > futureTrdQty;
    std::vector<RefCountedPtrT<MDLMessage> > optionTrdQty;
    std::vector<RefCountedPtrT<MDLMessage> > cmbTypeInfo;
    std::vector<RefCountedPtrT<MDLMessage> > optionParam;
    std::vector<RefCountedPtrT<MDLMessage> > futureOrder;
    std::vector<RefCountedPtrT<MDLMessage> > optionOrder;
    std::vector<RefCountedPtrT<MDLMessage> > cmbOrder;
} gfexl2_msg;

struct MDLSWGMsg {
    std::vector<RefCountedPtrT<MDLMessage> > index;
    std::vector<RefCountedPtrT<MDLMessage> > huobiMarketData;
    std::vector<RefCountedPtrT<MDLMessage> > huobiTrade;
    std::vector<RefCountedPtrT<MDLMessage> > huobiOrder;
    std::vector<RefCountedPtrT<MDLMessage> > huobiMinuteBar;
} swg_msg;

struct MDLNEEQMsg {
    std::vector<RefCountedPtrT<MDLMessage> > index;
    std::vector<RefCountedPtrT<MDLMessage> > stock;
    std::vector<RefCountedPtrT<MDLMessage> > matchedBargainOrder;
    std::vector<RefCountedPtrT<MDLMessage> > securityStatus;
    std::vector<RefCountedPtrT<MDLMessage> > nanHuaFuture;
    std::vector<RefCountedPtrT<MDLMessage> > fxcmForex;
    std::vector<RefCountedPtrT<MDLMessage> > bjStock;
    std::vector<RefCountedPtrT<MDLMessage> > bjBondSnapshot;
    std::vector<RefCountedPtrT<MDLMessage> > bjBondOrder011;
    std::vector<RefCountedPtrT<MDLMessage> > bjBondTrans01X;
    std::vector<RefCountedPtrT<MDLMessage> > bjBondSnapshotL1;
} neeq_msg;

struct MDLBARMsg {
    std::vector<RefCountedPtrT<MDLMessage> > xshgStockMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > xsheStockMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > hkexStockMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > dceFutureMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > shfeFutureMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > czceFutureMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > cffexFutureMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > xshgOptionMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > xshgIndexMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > xsheIndexMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > xshgCapitalFlow;
    std::vector<RefCountedPtrT<MDLMessage> > xsheCapitalFlow;
    std::vector<RefCountedPtrT<MDLMessage> > industryCapitalFlow;
    std::vector<RefCountedPtrT<MDLMessage> > xshgMoneyFlow;
    std::vector<RefCountedPtrT<MDLMessage> > xsheMoneyFlow;
    std::vector<RefCountedPtrT<MDLMessage> > xshgStockMultiMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > xsheStockMultiMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > hkexStockMultiMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > dceFutureMultiMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > shfeFutureMultiMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > czceFutureMultiMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > cffexFutureMultiMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > xshgOptionMultiMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > xshgIndexMultiMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > xsheIndexMultiMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > industryMoneyFlow;
    std::vector<RefCountedPtrT<MDLMessage> > hangSengIndexMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > deltaTick;
    std::vector<RefCountedPtrT<MDLMessage> > neeqMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > neeqMultiMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > dceFutureBar;
    std::vector<RefCountedPtrT<MDLMessage> > shfeFutureBar;
    std::vector<RefCountedPtrT<MDLMessage> > czceFutureBar;
    std::vector<RefCountedPtrT<MDLMessage> > cffexFutureBar;
    std::vector<RefCountedPtrT<MDLMessage> > czceOptionMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > czceOptionMultiMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > dceOptionMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > dceOptionMultiMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > czceOptionBar;
    std::vector<RefCountedPtrT<MDLMessage> > dceOptionBar;
    std::vector<RefCountedPtrT<MDLMessage> > nanHuaFutureMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > fxcmMinuteBar_V2;
    std::vector<RefCountedPtrT<MDLMessage> > dyFutureIndex;
    std::vector<RefCountedPtrT<MDLMessage> > dyThemeIndex;
    std::vector<RefCountedPtrT<MDLMessage> > shszAccuMainMoneyFlow;
    std::vector<RefCountedPtrT<MDLMessage> > swIndustry;
    std::vector<RefCountedPtrT<MDLMessage> > deltaTheme;
    std::vector<RefCountedPtrT<MDLMessage> > crudeFutureMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > crudeFutureSecondBar;
    std::vector<RefCountedPtrT<MDLMessage> > themeMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > shszAccuMoneyFlow;
    std::vector<RefCountedPtrT<MDLMessage> > shfeOptionMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > shfeOptionMultiMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > shfeOptionBar;
    std::vector<RefCountedPtrT<MDLMessage> > indexStatistics;
    std::vector<RefCountedPtrT<MDLMessage> > swgIndexMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > dyThemeIndexAA;
    std::vector<RefCountedPtrT<MDLMessage> > dyThemeMoneyFlow;
    std::vector<RefCountedPtrT<MDLMessage> > customTheme;
    std::vector<RefCountedPtrT<MDLMessage> > shszDeltaPrice;
    std::vector<RefCountedPtrT<MDLMessage> > ahComp;
    std::vector<RefCountedPtrT<MDLMessage> > shszAccuMoneyFlow2;
    std::vector<RefCountedPtrT<MDLMessage> > shszAccuMoneyFlow2Ext;
    std::vector<RefCountedPtrT<MDLMessage> > hkexIndexMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > marketMoneyFlow;
    std::vector<RefCountedPtrT<MDLMessage> > preMktLimitChange;
    std::vector<RefCountedPtrT<MDLMessage> > shszDynamicMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > cffexOptionBar;
    std::vector<RefCountedPtrT<MDLMessage> > xsheOptionBar;
    std::vector<RefCountedPtrT<MDLMessage> > cniIndexMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > shszAccuMoneyFlow2Extra;
    std::vector<RefCountedPtrT<MDLMessage> > shszMoneyFlow2PriceSpread;
    std::vector<RefCountedPtrT<MDLMessage> > dyThemeIndexExtra;
    std::vector<RefCountedPtrT<MDLMessage> > regionMoneyFlow;
    std::vector<RefCountedPtrT<MDLMessage> > csiIndexMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > deltaIndex;
    std::vector<RefCountedPtrT<MDLMessage> > orderDetailStat;
    std::vector<RefCountedPtrT<MDLMessage> > shszLimitStatus;
    std::vector<RefCountedPtrT<MDLMessage> > indexDynamicBar;
    std::vector<RefCountedPtrT<MDLMessage> > marketMetrics;
    std::vector<RefCountedPtrT<MDLMessage> > deltaPTFPTick;
    std::vector<RefCountedPtrT<MDLMessage> > indexMoneyFlow2;
    std::vector<RefCountedPtrT<MDLMessage> > fundsValuation;
    std::vector<RefCountedPtrT<MDLMessage> > crudeOptionMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > swIndustry2;
    std::vector<RefCountedPtrT<MDLMessage> > xsheBondMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > xshgBondMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > dyThemeIndex2;
    std::vector<RefCountedPtrT<MDLMessage> > gfexFutureMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > gfexOptionMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > themeMinuteBar2;
    std::vector<RefCountedPtrT<MDLMessage> > shszDeltaIndex;
    std::vector<RefCountedPtrT<MDLMessage> > dyThemeIndex3;
    std::vector<RefCountedPtrT<MDLMessage> > themeMinuteBar3;
    std::vector<RefCountedPtrT<MDLMessage> > bjBondBar;
    std::vector<RefCountedPtrT<MDLMessage> > dyThemeMoneyFlow3;
    std::vector<RefCountedPtrT<MDLMessage> > tickerChgPct;
    std::vector<RefCountedPtrT<MDLMessage> > bjStockMinuteBar;
    std::vector<RefCountedPtrT<MDLMessage> > etfMoneyFlow;
} bar_msg;

struct MDLSHNYMsg {
    std::vector<RefCountedPtrT<MDLMessage> > crudeFuture;
    std::vector<RefCountedPtrT<MDLMessage> > futurePlus;
    std::vector<RefCountedPtrT<MDLMessage> > crudeOption;
} shny_msg;

struct MDLDIGICURMsg {
    std::vector<RefCountedPtrT<MDLMessage> > geminiAuction;
    std::vector<RefCountedPtrT<MDLMessage> > geminiOrder;
    std::vector<RefCountedPtrT<MDLMessage> > geminiTick;
    std::vector<RefCountedPtrT<MDLMessage> > coinMarketCapTick;
    std::vector<RefCountedPtrT<MDLMessage> > coinMarketCapGlobal;
    std::vector<RefCountedPtrT<MDLMessage> > krakenOrder;
    std::vector<RefCountedPtrT<MDLMessage> > krakenTick;
    std::vector<RefCountedPtrT<MDLMessage> > krakenTrans;
    std::vector<RefCountedPtrT<MDLMessage> > bitstampFullOrder;
    std::vector<RefCountedPtrT<MDLMessage> > bitstampTick;
    std::vector<RefCountedPtrT<MDLMessage> > bitstampTrans;
    std::vector<RefCountedPtrT<MDLMessage> > gdaxLevel2Order;
    std::vector<RefCountedPtrT<MDLMessage> > gdaxTick;
    std::vector<RefCountedPtrT<MDLMessage> > gdaxTrans;
    std::vector<RefCountedPtrT<MDLMessage> > itbitOrder;
    std::vector<RefCountedPtrT<MDLMessage> > itbitTick;
    std::vector<RefCountedPtrT<MDLMessage> > itbitTrans;
    std::vector<RefCountedPtrT<MDLMessage> > cryFacilitiesOrder;
    std::vector<RefCountedPtrT<MDLMessage> > cryFacilitiesTick;
} digicur_msg;

struct MDLCSIMsg {
    std::vector<RefCountedPtrT<MDLMessage> > csIndex;
    std::vector<RefCountedPtrT<MDLMessage> > etfIopv;
} csi_msg;

struct MDLCNIMsg {
    std::vector<RefCountedPtrT<MDLMessage> > snapshot360001;
} cni_msg;


class MyMessageHandler : public MessageHandler {
public:
    using CallbackFunc = std::function<void(int, const MDLMessage *)>;

    bool Open(const char *address) {
        // int num_work_threads = 4;
        int num_work_threads = 1;
        m_IOManager = CreateIOManager(num_work_threads);
        if (m_IOManager.IsNull()) {
            printf("Incompatible API lib version.\n");
            return false;
        }
        m_IOManager->EnableLog("./Myserver/mdl");
        // bool multi_thread_callback = true; // set to true if num_work_threads > 1 and OnXXXMessage() is thread-safe
        bool multi_thread_callback = false; // set to true if num_work_threads > 1 and OnXXXMessage() is thread-safe
        SubscriberPtr sub = m_IOManager->CreateSubscriber(this, multi_thread_callback);
        sub->SetServerAddress(address);
        if (mdl_conf.Token != "Nil") {
            sub->SetUserName(mdl_conf.Token.c_str());
        }
        sub->SetMessageEncoding(MDLEID_MKTPRO);

        for (const auto &SHL1MsgID: mdl_conf.SHL1) {
            std::cout << "subscribe shl1 msg id: " << SHL1MsgID << std::endl;
            switch (SHL1MsgID) {
                case mdl_shl1_msg::MDLMID_MDL_SHL1_SHL1Index:
                    sub->SubcribeMessage<mdl_shl1_msg::SHL1Index>();
                    break;
                case mdl_shl1_msg::MDLMID_MDL_SHL1_SHL1Stock:
                    sub->SubcribeMessage<mdl_shl1_msg::SHL1Stock>();
                    break;
                case mdl_shl1_msg::MDLMID_MDL_SHL1_Indexes:
                    sub->SubcribeMessage<mdl_shl1_msg::Indexes>();
                    break;
                case mdl_shl1_msg::MDLMID_MDL_SHL1_Equity:
                    sub->SubcribeMessage<mdl_shl1_msg::Equity>();
                    break;
                case mdl_shl1_msg::MDLMID_MDL_SHL1_Bonds:
                    sub->SubcribeMessage<mdl_shl1_msg::Bonds>();
                    break;
                case mdl_shl1_msg::MDLMID_MDL_SHL1_Funds:
                    sub->SubcribeMessage<mdl_shl1_msg::Funds>();
                    break;
                case mdl_shl1_msg::MDLMID_MDL_SHL1_Level1Plus:
                    sub->SubcribeMessage<mdl_shl1_msg::Level1Plus>();
                    break;
                case mdl_shl1_msg::MDLMID_MDL_SHL1_Equity2:
                    sub->SubcribeMessage<mdl_shl1_msg::Equity2>();
                    break;
                case mdl_shl1_msg::MDLMID_MDL_SHL1_Bond2:
                    sub->SubcribeMessage<mdl_shl1_msg::Bond2>();
                    break;
                case mdl_shl1_msg::MDLMID_MDL_SHL1_Fund2:
                    sub->SubcribeMessage<mdl_shl1_msg::Fund2>();
                    break;
                case mdl_shl1_msg::MDLMID_MDL_SHL1_Comp:
                    sub->SubcribeMessage<mdl_shl1_msg::Comp>();
                    break;
                case mdl_shl1_msg::MDLMID_MDL_SHL1_BondDist:
                    sub->SubcribeMessage<mdl_shl1_msg::BondDist>();
                    break;
                case mdl_shl1_msg::MDLMID_MDL_SHL1_ATP:
                    sub->SubcribeMessage<mdl_shl1_msg::ATP>();
                    break;
                default:
                    break;
            }
        }

        for (const auto &SHL2MsgID: mdl_conf.SHL2) {
            std::cout << "subscribe shl2 msg id: " << SHL2MsgID << std::endl;
            switch (SHL2MsgID) {
                case mdl_shl2_msg::MDLMID_MDL_SHL2_SHL1Stock:
                    sub->SubcribeMessage<mdl_shl2_msg::SHL1Stock>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_SHL2Transaction:
                    sub->SubcribeMessage<mdl_shl2_msg::SHL2Transaction>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_SHL2MarketData:
                    sub->SubcribeMessage<mdl_shl2_msg::SHL2MarketData>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_SHL2VirtualAuctionPrice:
                    sub->SubcribeMessage<mdl_shl2_msg::SHL2VirtualAuctionPrice>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_SHL2Index:
                    sub->SubcribeMessage<mdl_shl2_msg::SHL2Index>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_SHL2MarketOverview:
                    sub->SubcribeMessage<mdl_shl2_msg::SHL2MarketOverview>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_SHL2Statics:
                    sub->SubcribeMessage<mdl_shl2_msg::SHL2Statics>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_OPTLevel1:
                    sub->SubcribeMessage<mdl_shl2_msg::OPTLevel1>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_MDSnapshotFullRefresh_4001:
                    sub->SubcribeMessage<mdl_shl2_msg::MDSnapshotFullRefresh_4001>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_FixedIncomePrice:
                    sub->SubcribeMessage<mdl_shl2_msg::FixedIncomePrice>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_FixedIncomeInfo:
                    sub->SubcribeMessage<mdl_shl2_msg::FixedIncomeInfo>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_FixedIncomeTransaction:
                    sub->SubcribeMessage<mdl_shl2_msg::FixedIncomeTransaction>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_FixedIncomeFirmQuote:
                    sub->SubcribeMessage<mdl_shl2_msg::FixedIncomeFirmQuote>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_RealQuota:
                    sub->SubcribeMessage<mdl_shl2_msg::RealQuota>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_ATPMarketData:
                    sub->SubcribeMessage<mdl_shl2_msg::ATPMarketData>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_ATPTransaction:
                    sub->SubcribeMessage<mdl_shl2_msg::ATPTransaction>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_SHL2Transaction2:
                    sub->SubcribeMessage<mdl_shl2_msg::SHL2Transaction2>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_Order:
                    sub->SubcribeMessage<mdl_shl2_msg::Order>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_BondMarketData:
                    sub->SubcribeMessage<mdl_shl2_msg::BondMarketData>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_BondTick:
                    sub->SubcribeMessage<mdl_shl2_msg::BondTick>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_BondOrderQueue:
                    sub->SubcribeMessage<mdl_shl2_msg::BondOrderQueue>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_MarketInfo:
                    sub->SubcribeMessage<mdl_shl2_msg::MarketInfo>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_NGTSTick:
                    sub->SubcribeMessage<mdl_shl2_msg::NGTSTick>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_ETFMarketData:
                    sub->SubcribeMessage<mdl_shl2_msg::ETFMarketData>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_BondDist:
                    sub->SubcribeMessage<mdl_shl2_msg::BondDist>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_BondDistOrderQueue:
                    sub->SubcribeMessage<mdl_shl2_msg::BondDistOrderQueue>();
                    break;
                case mdl_shl2_msg::MDLMID_MDL_SHL2_BondDistTick:
                    sub->SubcribeMessage<mdl_shl2_msg::BondDistTick>();
                    break;
                default:
                    break;
            }
        }

        for (const auto &SZL1MsgID: mdl_conf.SZL1) {
            std::cout << "subscribe szl1 msg id: " << SZL1MsgID << std::endl;
            switch (SZL1MsgID) {
                case mdl_szl1_msg::MDLMID_MDL_SZL1_SZL1Index:
                    sub->SubcribeMessage<mdl_szl1_msg::SZL1Index>();
                    break;
                case mdl_szl1_msg::MDLMID_MDL_SZL1_SZL1Stock:
                    sub->SubcribeMessage<mdl_szl1_msg::SZL1Stock>();
                    break;
                case mdl_szl1_msg::MDLMID_MDL_SZL1_Level1Plus:
                    sub->SubcribeMessage<mdl_szl1_msg::Level1Plus>();
                    break;
                case mdl_szl1_msg::MDLMID_MDL_SZL1_SZL1Option:
                    sub->SubcribeMessage<mdl_szl1_msg::SZL1Option>();
                    break;
                case mdl_szl1_msg::MDLMID_MDL_SZL1_SZL1Option2:
                    sub->SubcribeMessage<mdl_szl1_msg::SZL1Option2>();
                    break;
                case mdl_szl1_msg::MDLMID_MDL_SZL1_Bonds:
                    sub->SubcribeMessage<mdl_szl1_msg::Bonds>();
                    break;
                case mdl_szl1_msg::MDLMID_MDL_SZL1_Order300392:
                    sub->SubcribeMessage<mdl_szl1_msg::Order300392>();
                    break;
                case mdl_szl1_msg::MDLMID_MDL_SZL1_Transaction300391:
                    sub->SubcribeMessage<mdl_szl1_msg::Transaction300391>();
                    break;
                case mdl_szl1_msg::MDLMID_MDL_SZL1_BondDistribution:
                    sub->SubcribeMessage<mdl_szl1_msg::BondDistribution>();
                    break;
                case mdl_szl1_msg::MDLMID_MDL_SZL1_ATP:
                    sub->SubcribeMessage<mdl_szl1_msg::ATP>();
                    break;
                case mdl_szl1_msg::MDLMID_MDL_SZL1_Index2:
                    sub->SubcribeMessage<mdl_szl1_msg::Index2>();
                    break;
                case mdl_szl1_msg::MDLMID_MDL_SZL1_Snapshot309211:
                    sub->SubcribeMessage<mdl_szl1_msg::Snapshot309211>();
                    break;
                default:
                    break;
            }
        }

        for(const auto &SZL2MsgID: mdl_conf.SZL2) {
    std::cout << "subscribe szl2 msg id: " << SZL2MsgID << std::endl;
    switch (SZL2MsgID) {
        case mdl_szl2_msg::MDLMID_MDL_SZL2_Snapshot300111:
            sub->SubcribeMessage<mdl_szl2_msg::Snapshot300111_v2>();
            break;
        case mdl_szl2_msg::MDLMID_MDL_SZL2_Snapshot309011:
            sub->SubcribeMessage<mdl_szl2_msg::Snapshot309011_v2>();
            break;
        case mdl_szl2_msg::MDLMID_MDL_SZL2_Snapshot309111:
            sub->SubcribeMessage<mdl_szl2_msg::Snapshot309111_v2>();
            break;
        case mdl_szl2_msg::MDLMID_MDL_SZL2_Snapshot300611:
            sub->SubcribeMessage<mdl_szl2_msg::Snapshot300611_v2>();
            break;
        case mdl_szl2_msg::MDLMID_MDL_SZL2_Order300192:
            sub->SubcribeMessage<mdl_szl2_msg::Order300192_v2>();
            break;
        case mdl_szl2_msg::MDLMID_MDL_SZL2_Order300592:
            sub->SubcribeMessage<mdl_szl2_msg::Order300592_v2>();
            break;
        case mdl_szl2_msg::MDLMID_MDL_SZL2_Order300792:
            sub->SubcribeMessage<mdl_szl2_msg::Order300792_v2>();
            break;
        case mdl_szl2_msg::MDLMID_MDL_SZL2_Transaction300191:
            sub->SubcribeMessage<mdl_szl2_msg::Transaction300191_v2>();
            break;
        case mdl_szl2_msg::MDLMID_MDL_SZL2_Transaction300591:
            sub->SubcribeMessage<mdl_szl2_msg::Transaction300591_v2>();
            break;
        case mdl_szl2_msg::MDLMID_MDL_SZL2_Transaction300791:
            sub->SubcribeMessage<mdl_szl2_msg::Transaction300791_v2>();
            break;
        case mdl_szl2_msg::MDLMID_MDL_SZL2_Snapshot300111_v3:
            sub->SubcribeMessage<mdl_szl2_msg::Snapshot300111_v3>();
            break;
        case mdl_szl2_msg::MDLMID_MDL_SZL2_RealQuota:
            sub->SubcribeMessage<mdl_szl2_msg::RealQuota>();
            break;
        case mdl_szl2_msg::MDLMID_MDL_SZL2_Snapshot306311:
            sub->SubcribeMessage<mdl_szl2_msg::Snapshot306311>();
            break;
        case mdl_szl2_msg::MDLMID_MDL_SZL2_Snapshot300211:
            sub->SubcribeMessage<mdl_szl2_msg::Snapshot300211>();
            break;
        case mdl_szl2_msg::MDLMID_MDL_SZL2_Order300292:
            sub->SubcribeMessage<mdl_szl2_msg::Order300292>();
            break;
        case mdl_szl2_msg::MDLMID_MDL_SZL2_Transaction300291:
            sub->SubcribeMessage<mdl_szl2_msg::Transaction300291>();
            break;
        case mdl_szl2_msg::MDLMID_MDL_SZL2_Order300392:
            sub->SubcribeMessage<mdl_szl2_msg::Order300392>();
            break;
        case mdl_szl2_msg::MDLMID_MDL_SZL2_Transaction300391:
            sub->SubcribeMessage<mdl_szl2_msg::Transaction300391>();
            break;
        case mdl_szl2_msg::MDLMID_MDL_SZL2_BondOrderQueue:
            sub->SubcribeMessage<mdl_szl2_msg::BondOrderQueue>();
            break;
        case mdl_szl2_msg::MDLMID_MDL_SZL2_BondDistribution:
            sub->SubcribeMessage<mdl_szl2_msg::BondDistribution>();
            break;
        case mdl_szl2_msg::MDLMID_MDL_SZL2_Snapshot309211:
            sub->SubcribeMessage<mdl_szl2_msg::Snapshot309211>();
            break;
        case mdl_szl2_msg::MDLMID_MDL_SZL2_ETFData:
            sub->SubcribeMessage<mdl_szl2_msg::ETFData>();
            break;
        case mdl_szl2_msg::MDLMID_MDL_SZL2_MarketInfo:
            sub->SubcribeMessage<mdl_szl2_msg::MarketInfo>();
            break;
        default:
            break;
    }
}

        for (const auto &DCEMsgID: mdl_conf.DCE) {
            std::cout << "subscribe dce msg id: " << DCEMsgID << std::endl;
            switch (DCEMsgID) {
                case mdl_dce_msg::MDLMID_MDL_DCE_CTPFuture:
                    sub->SubcribeMessage<mdl_dce_msg::CTPFuture>();
                    break;
                case mdl_dce_msg::MDLMID_MDL_DCE_Future:
                    sub->SubcribeMessage<mdl_dce_msg::Future>();
                    break;
                case mdl_dce_msg::MDLMID_MDL_DCE_FuturePlus:
                    sub->SubcribeMessage<mdl_dce_msg::FuturePlus>();
                    break;
                case mdl_dce_msg::MDLMID_MDL_DCE_CTPOption:
                    sub->SubcribeMessage<mdl_dce_msg::CTPOption>();
                    break;
                case mdl_dce_msg::MDLMID_MDL_DCE_Indexes:
                    sub->SubcribeMessage<mdl_dce_msg::Indexes>();
                    break;
                case mdl_dce_msg::MDLMID_MDL_DCE_Index2:
                    sub->SubcribeMessage<mdl_dce_msg::Index2>();
                    break;
                default:
                    break;
            }
        }

        for (const auto &DCEL2MsgID: mdl_conf.DCEL2) {
            std::cout << "subscribe dcel2 msg id: " << DCEL2MsgID << std::endl;
            switch (DCEL2MsgID) {
                case mdl_dcel2_msg::MDLMID_MDL_DCEL2_Future:
                    sub->SubcribeMessage<mdl_dcel2_msg::Future>();
                    break;
                case mdl_dcel2_msg::MDLMID_MDL_DCEL2_Option:
                    sub->SubcribeMessage<mdl_dcel2_msg::Option>();
                    break;
                case mdl_dcel2_msg::MDLMID_MDL_DCEL2_FutureTrdQty:
                    sub->SubcribeMessage<mdl_dcel2_msg::FutureTrdQty>();
                    break;
                case mdl_dcel2_msg::MDLMID_MDL_DCEL2_OptionTrdQty:
                    sub->SubcribeMessage<mdl_dcel2_msg::OptionTrdQty>();
                    break;
                case mdl_dcel2_msg::MDLMID_MDL_DCEL2_CmbTypeInfo:
                    sub->SubcribeMessage<mdl_dcel2_msg::CmbTypeInfo>();
                    break;
                case mdl_dcel2_msg::MDLMID_MDL_DCEL2_OptionParam:
                    sub->SubcribeMessage<mdl_dcel2_msg::OptionParam>();
                    break;
                case mdl_dcel2_msg::MDLMID_MDL_DCEL2_FutureOrder:
                    sub->SubcribeMessage<mdl_dcel2_msg::FutureOrder>();
                    break;
                case mdl_dcel2_msg::MDLMID_MDL_DCEL2_OptionOrder:
                    sub->SubcribeMessage<mdl_dcel2_msg::OptionOrder>();
                    break;
                case mdl_dcel2_msg::MDLMID_MDL_DCEL2_CmbOrder:
                    sub->SubcribeMessage<mdl_dcel2_msg::CmbOrder>();
                    break;
                case mdl_dcel2_msg::MDLMID_MDL_DCEL2_OrderStat:
                    sub->SubcribeMessage<mdl_dcel2_msg::OrderStat>();
                    break;
                default:
                    break;
            }
        }

        for (const auto &SHFEMsgID: mdl_conf.SHFE) {
            std::cout << "subscribe shfe msg id: " << SHFEMsgID << std::endl;
            switch (SHFEMsgID) {
                case mdl_shfe_msg::MDLMID_MDL_SHFE_CTPFuture:
                    sub->SubcribeMessage<mdl_shfe_msg::CTPFuture>();
                    break;
                case mdl_shfe_msg::MDLMID_MDL_SHFE_Future:
                    sub->SubcribeMessage<mdl_shfe_msg::Future>();
                    break;
                case mdl_shfe_msg::MDLMID_MDL_SHFE_FuturePlus:
                    sub->SubcribeMessage<mdl_shfe_msg::FuturePlus>();
                    break;
                case mdl_shfe_msg::MDLMID_MDL_SHFE_CTPOption:
                    sub->SubcribeMessage<mdl_shfe_msg::CTPOption>();
                    break;
                case mdl_shfe_msg::MDLMID_MDL_SHFE_Index:
                    sub->SubcribeMessage<mdl_shfe_msg::Index>();
                    break;
                default:
                    break;
            }
        }

        for (const auto &SHFEL2MsgID: mdl_conf.SHFEL2) {
            std::cout << "subscribe shfel2 msg id: " << SHFEL2MsgID << std::endl;
            switch (SHFEL2MsgID) {
                case mdl_shfel2_msg::MDLMID_MDL_SHFEL2_CTPFuture:
                    sub->SubcribeMessage<mdl_shfel2_msg::CTPFuture>();
                    break;
                case mdl_shfel2_msg::MDLMID_MDL_SHFEL2_CTPOption:
                    sub->SubcribeMessage<mdl_shfel2_msg::CTPOption>();
                    break;
                case mdl_shfel2_msg::MDLMID_MDL_SHFEL2_CrudeFuture:
                    sub->SubcribeMessage<mdl_shfel2_msg::CrudeFuture>();
                    break;
                case mdl_shfel2_msg::MDLMID_MDL_SHFEL2_CrudeOption:
                    sub->SubcribeMessage<mdl_shfel2_msg::CrudeOption>();
                    break;
                default:
                    break;
            }
        }

        for (const auto &CZCEMsgID: mdl_conf.CZCE) {
            std::cout << "subscribe czce msg id: " << CZCEMsgID << std::endl;
            switch (CZCEMsgID) {
                case mdl_czce_msg::MDLMID_MDL_CZCE_CTPFuture:
                    sub->SubcribeMessage<mdl_czce_msg::CTPFuture>();
                    break;
                case mdl_czce_msg::MDLMID_MDL_CZCE_Future:
                    sub->SubcribeMessage<mdl_czce_msg::Future>();
                    break;
                case mdl_czce_msg::MDLMID_MDL_CZCE_CZCEFuture:
                    sub->SubcribeMessage<mdl_czce_msg::CZCEFuture>();
                    break;
                case mdl_czce_msg::MDLMID_MDL_CZCE_Level2Future:
                    sub->SubcribeMessage<mdl_czce_msg::Level2Future>();
                    break;
                case mdl_czce_msg::MDLMID_MDL_CZCE_FuturePlus:
                    sub->SubcribeMessage<mdl_czce_msg::FuturePlus>();
                    break;
                case mdl_czce_msg::MDLMID_MDL_CZCE_CTPOption:
                    sub->SubcribeMessage<mdl_czce_msg::CTPOption>();
                    break;
                default:
                    break;
            }
        }

        for (const auto &CZCEL2MsgID: mdl_conf.CZCEL2) {
            std::cout << "subscribe czcel2 msg id: " << CZCEL2MsgID << std::endl;
            switch (CZCEL2MsgID) {
                case mdl_czcel2_msg::MDLMID_MDL_CZCEL2_CTPFuture:
                    sub->SubcribeMessage<mdl_czcel2_msg::CTPFuture>();
                    break;
                case mdl_czcel2_msg::MDLMID_MDL_CZCEL2_CTPOption:
                    sub->SubcribeMessage<mdl_czcel2_msg::CTPOption>();
                    break;
                case mdl_czcel2_msg::MDLMID_MDL_CZCEL2_FutureTrdQtyStatus:
                    sub->SubcribeMessage<mdl_czcel2_msg::FutureTrdQtyStatus>();
                    break;
                case mdl_czcel2_msg::MDLMID_MDL_CZCEL2_OptionTrdQtyStatus:
                    sub->SubcribeMessage<mdl_czcel2_msg::OptionTrdQtyStatus>();
                    break;
                case mdl_czcel2_msg::MDLMID_MDL_CZCEL2_CmbTypeInfo:
                    sub->SubcribeMessage<mdl_czcel2_msg::CmbTypeInfo>();
                    break;
                case mdl_czcel2_msg::MDLMID_MDL_CZCEL2_OptionParam:
                    sub->SubcribeMessage<mdl_czcel2_msg::OptionParam>();
                    break;
                case mdl_czcel2_msg::MDLMID_MDL_CZCEL2_TradeStatus:
                    sub->SubcribeMessage<mdl_czcel2_msg::TradeStatus>();
                    break;
                default:
                    break;
            }
        }

        for (const auto &CFFEXMsgID: mdl_conf.CFFEX) {
            std::cout << "subscribe cffex msg id: " << CFFEXMsgID << std::endl;
            switch (CFFEXMsgID) {
                case mdl_cffex_msg::MDLMID_MDL_CFFEX_CTPFuture:
                    sub->SubcribeMessage<mdl_cffex_msg::CTPFuture>();
                    break;
                case mdl_cffex_msg::MDLMID_MDL_CFFEX_Future:
                    sub->SubcribeMessage<mdl_cffex_msg::Future>();
                    break;
                case mdl_cffex_msg::MDLMID_MDL_CFFEX_FuturePlus:
                    sub->SubcribeMessage<mdl_cffex_msg::FuturePlus>();
                    break;
                case mdl_cffex_msg::MDLMID_MDL_CFFEX_Option:
                    sub->SubcribeMessage<mdl_cffex_msg::Option>();
                    break;
                default:
                    break;
            }
        }

        for (const auto &CFFEXL2MsgID: mdl_conf.CFFEXL2) {
            std::cout << "subscribe cffexl2 msg id: " << CFFEXL2MsgID << std::endl;
            switch (CFFEXL2MsgID) {
                case mdl_cffexl2_msg::MDLMID_MDL_CFFEXL2_Future:
                    sub->SubcribeMessage<mdl_cffexl2_msg::Future>();
                    break;
                case mdl_cffexl2_msg::MDLMID_MDL_CFFEXL2_Option:
                    sub->SubcribeMessage<mdl_cffexl2_msg::Option>();
                    break;
                default:
                    break;
            }
        }

        for (const auto &HKEXMsgID: mdl_conf.HKEX) {
            std::cout << "subscribe hkex msg id: " << HKEXMsgID << std::endl;
            switch (HKEXMsgID) {
                case mdl_hkex_msg::MDLMID_MDL_HKEX_OMDMarketData:
                    sub->SubcribeMessage<mdl_hkex_msg::OMDMarketData>();
                    break;
                case mdl_hkex_msg::MDLMID_MDL_HKEX_HangSengIndex:
                    sub->SubcribeMessage<mdl_hkex_msg::HangSengIndex>();
                    break;
                case mdl_hkex_msg::MDLMID_MDL_HKEX_OMDClosingPrice:
                    sub->SubcribeMessage<mdl_hkex_msg::OMDClosingPrice>();
                    break;
                case mdl_hkex_msg::MDLMID_MDL_HKEX_SCMarketTurnover:
                    sub->SubcribeMessage<mdl_hkex_msg::SCMarketTurnover>();
                    break;
                case mdl_hkex_msg::MDLMID_MDL_HKEX_SCDailyQuotaBalance:
                    sub->SubcribeMessage<mdl_hkex_msg::SCDailyQuotaBalance>();
                    break;
                case mdl_hkex_msg::MDLMID_MDL_HKEX_OMDIndexData:
                    sub->SubcribeMessage<mdl_hkex_msg::OMDIndexData>();
                    break;
                case mdl_hkex_msg::MDLMID_MDL_HKEX_OMDSecurityDefinition:
                    sub->SubcribeMessage<mdl_hkex_msg::OMDSecurityDefinition>();
                    break;
                case mdl_hkex_msg::MDLMID_MDL_HKEX_OMDTradeTicker:
                    sub->SubcribeMessage<mdl_hkex_msg::OMDTradeTicker>();
                    break;
                case mdl_hkex_msg::MDLMID_MDL_HKEX_BrokerQueue:
                    sub->SubcribeMessage<mdl_hkex_msg::BrokerQueue>();
                    break;
                case mdl_hkex_msg::MDLMID_MDL_HKEX_OMDOrderBook:
                    sub->SubcribeMessage<mdl_hkex_msg::OMDOrderBook>();
                    break;
                default:
                    break;
            }
        }

        for (const auto &GFEXMsgID: mdl_conf.GFEX) {
            std::cout << "subscribe gfex msg id: " << GFEXMsgID << std::endl;
            switch (GFEXMsgID) {
                case mdl_gfex_msg::MDLMID_MDL_GFEX_Future:
                    sub->SubcribeMessage<mdl_gfex_msg::Future>();
                    break;
                case mdl_gfex_msg::MDLMID_MDL_GFEX_Option:
                    sub->SubcribeMessage<mdl_gfex_msg::Option>();
                    break;
                case mdl_gfex_msg::MDLMID_MDL_GFEX_FuturePlus:
                    sub->SubcribeMessage<mdl_gfex_msg::FuturePlus>();
                    break;
                case mdl_gfex_msg::MDLMID_MDL_GFEX_CmbTypeInfo:
                    sub->SubcribeMessage<mdl_gfex_msg::CmbTypeInfo>();
                    break;
                default:
                    break;
            }
        }

        for (const auto &GFEXL2MsgID: mdl_conf.GFEXL2) {
            std::cout << "subscribe gfexl2 msg id: " << GFEXL2MsgID << std::endl;
            switch (GFEXL2MsgID) {
                case mdl_gfexl2_msg::MDLMID_MDL_GFEXL2_Future:
                    sub->SubcribeMessage<mdl_gfexl2_msg::Future>();
                    break;
                case mdl_gfexl2_msg::MDLMID_MDL_GFEXL2_Option:
                    sub->SubcribeMessage<mdl_gfexl2_msg::Option>();
                    break;
                case mdl_gfexl2_msg::MDLMID_MDL_GFEXL2_FutureTrdQty:
                    sub->SubcribeMessage<mdl_gfexl2_msg::FutureTrdQty>();
                    break;
                case mdl_gfexl2_msg::MDLMID_MDL_GFEXL2_OptionTrdQty:
                    sub->SubcribeMessage<mdl_gfexl2_msg::OptionTrdQty>();
                    break;
                case mdl_gfexl2_msg::MDLMID_MDL_GFEXL2_CmbTypeInfo:
                    sub->SubcribeMessage<mdl_gfexl2_msg::CmbTypeInfo>();
                    break;
                case mdl_gfexl2_msg::MDLMID_MDL_GFEXL2_OptionParam:
                    sub->SubcribeMessage<mdl_gfexl2_msg::OptionParam>();
                    break;
                case mdl_gfexl2_msg::MDLMID_MDL_GFEXL2_FutureOrder:
                    sub->SubcribeMessage<mdl_gfexl2_msg::FutureOrder>();
                    break;
                case mdl_gfexl2_msg::MDLMID_MDL_GFEXL2_OptionOrder:
                    sub->SubcribeMessage<mdl_gfexl2_msg::OptionOrder>();
                    break;
                case mdl_gfexl2_msg::MDLMID_MDL_GFEXL2_CmbOrder:
                    sub->SubcribeMessage<mdl_gfexl2_msg::CmbOrder>();
                    break;
                default:
                    break;
            }
        }

        for (const auto &SWGMsgID: mdl_conf.SWG) {
            std::cout << "subscribe swg msg id: " << SWGMsgID << std::endl;
            switch (SWGMsgID) {
                case mdl_swg_msg::MDLMID_MDL_SWG_Index:
                    sub->SubcribeMessage<mdl_swg_msg::Index>();
                    break;
                case mdl_swg_msg::MDLMID_MDL_SWG_HuobiMarketData:
                    sub->SubcribeMessage<mdl_swg_msg::HuobiMarketData>();
                    break;
                case mdl_swg_msg::MDLMID_MDL_SWG_HuobiTrade:
                    sub->SubcribeMessage<mdl_swg_msg::HuobiTrade>();
                    break;
                case mdl_swg_msg::MDLMID_MDL_SWG_HuobiOrder:
                    sub->SubcribeMessage<mdl_swg_msg::HuobiOrder>();
                    break;
                case mdl_swg_msg::MDLMID_MDL_SWG_HuobiMinuteBar:
                    sub->SubcribeMessage<mdl_swg_msg::HuobiMinuteBar>();
                    break;
                default:
                    break;
            }
        }

        for (const auto &NEEQMsgID: mdl_conf.NEEQ) {
            std::cout << "subscribe neeq msg id: " << NEEQMsgID << std::endl;
            switch (NEEQMsgID) {
                case mdl_neeq_msg::MDLMID_MDL_NEEQ_Index:
                    sub->SubcribeMessage<mdl_neeq_msg::Index>();
                    break;
                case mdl_neeq_msg::MDLMID_MDL_NEEQ_Stock:
                    sub->SubcribeMessage<mdl_neeq_msg::Stock>();
                    break;
                case mdl_neeq_msg::MDLMID_MDL_NEEQ_MatchedBargainOrder:
                    sub->SubcribeMessage<mdl_neeq_msg::MatchedBargainOrder>();
                    break;
                case mdl_neeq_msg::MDLMID_MDL_NEEQ_SecurityStatus:
                    sub->SubcribeMessage<mdl_neeq_msg::SecurityStatus>();
                    break;
                case mdl_neeq_msg::MDLMID_MDL_NEEQ_NanHuaFuture:
                    sub->SubcribeMessage<mdl_neeq_msg::NanHuaFuture>();
                    break;
                case mdl_neeq_msg::MDLMID_MDL_NEEQ_FXCMForex:
                    sub->SubcribeMessage<mdl_neeq_msg::FXCMForex>();
                    break;
                case mdl_neeq_msg::MDLMID_MDL_NEEQ_BJStock:
                    sub->SubcribeMessage<mdl_neeq_msg::BJStock>();
                    break;
                case mdl_neeq_msg::MDLMID_MDL_NEEQ_BJBondSnapshot:
                    sub->SubcribeMessage<mdl_neeq_msg::BJBondSnapshot>();
                    break;
                case mdl_neeq_msg::MDLMID_MDL_NEEQ_BJBondOrder011:
                    sub->SubcribeMessage<mdl_neeq_msg::BJBondOrder011>();
                    break;
                case mdl_neeq_msg::MDLMID_MDL_NEEQ_BJBondTrans01X:
                    sub->SubcribeMessage<mdl_neeq_msg::BJBondTrans01X>();
                    break;
                case mdl_neeq_msg::MDLMID_MDL_NEEQ_BJBondSnapshotL1:
                    sub->SubcribeMessage<mdl_neeq_msg::BJBondSnapshotL1>();
                    break;
                default:
                    break;
            }
        }

        for (const auto &BARMsgID: mdl_conf.BAR) {
            std::cout << "subscribe bar msg id: " << BARMsgID << std::endl;
            switch (BARMsgID) {
                case mdl_bar_msg::MDLMID_MDL_BAR_XSHGStockMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::XSHGStockMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_XSHEStockMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::XSHEStockMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_HKEXStockMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::HKEXStockMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_DCEFutureMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::DCEFutureMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_SHFEFutureMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::SHFEFutureMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_CZCEFutureMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::CZCEFutureMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_CFFEXFutureMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::CFFEXFutureMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_XSHGOptionMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::XSHGOptionMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_XSHGIndexMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::XSHGIndexMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_XSHEIndexMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::XSHEIndexMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_XSHGCapitalFlow:
                    sub->SubcribeMessage<mdl_bar_msg::XSHGCapitalFlow>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_XSHECapitalFlow:
                    sub->SubcribeMessage<mdl_bar_msg::XSHECapitalFlow>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_IndustryCapitalFlow:
                    sub->SubcribeMessage<mdl_bar_msg::IndustryCapitalFlow>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_XSHGMoneyFlow:
                    sub->SubcribeMessage<mdl_bar_msg::XSHGMoneyFlow>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_XSHEMoneyFlow:
                    sub->SubcribeMessage<mdl_bar_msg::XSHEMoneyFlow>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_XSHGStockMultiMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::XSHGStockMultiMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_XSHEStockMultiMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::XSHEStockMultiMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_HKEXStockMultiMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::HKEXStockMultiMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_DCEFutureMultiMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::DCEFutureMultiMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_SHFEFutureMultiMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::SHFEFutureMultiMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_CZCEFutureMultiMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::CZCEFutureMultiMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_CFFEXFutureMultiMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::CFFEXFutureMultiMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_XSHGOptionMultiMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::XSHGOptionMultiMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_XSHGIndexMultiMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::XSHGIndexMultiMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_XSHEIndexMultiMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::XSHEIndexMultiMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_IndustryMoneyFlow:
                    sub->SubcribeMessage<mdl_bar_msg::IndustryMoneyFlow>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_HangSengIndexMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::HangSengIndexMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_DeltaTick:
                    sub->SubcribeMessage<mdl_bar_msg::DeltaTick>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_NeeqMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::NeeqMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_NeeqMultiMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::NeeqMultiMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_DCEFutureBar:
                    sub->SubcribeMessage<mdl_bar_msg::DCEFutureBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_SHFEFutureBar:
                    sub->SubcribeMessage<mdl_bar_msg::SHFEFutureBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_CZCEFutureBar:
                    sub->SubcribeMessage<mdl_bar_msg::CZCEFutureBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_CFFEXFutureBar:
                    sub->SubcribeMessage<mdl_bar_msg::CFFEXFutureBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_CZCEOptionMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::CZCEOptionMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_CZCEOptionMultiMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::CZCEOptionMultiMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_DCEOptionMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::DCEOptionMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_DCEOptionMultiMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::DCEOptionMultiMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_CZCEOptionBar:
                    sub->SubcribeMessage<mdl_bar_msg::CZCEOptionBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_DCEOptionBar:
                    sub->SubcribeMessage<mdl_bar_msg::DCEOptionBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_NanHuaFutureMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::NanHuaFutureMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_FXCMMinuteBar_V2:
                    sub->SubcribeMessage<mdl_bar_msg::FXCMMinuteBar_V2>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_DYFutureIndex:
                    sub->SubcribeMessage<mdl_bar_msg::DYFutureIndex>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_DYThemeIndex:
                    sub->SubcribeMessage<mdl_bar_msg::DYThemeIndex>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_SHSZAccuMainMoneyFlow:
                    sub->SubcribeMessage<mdl_bar_msg::SHSZAccuMainMoneyFlow>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_SWIndustry:
                    sub->SubcribeMessage<mdl_bar_msg::SWIndustry>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_DeltaTheme:
                    sub->SubcribeMessage<mdl_bar_msg::DeltaTheme>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_CrudeFutureMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::CrudeFutureMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_CrudeFutureSecondBar:
                    sub->SubcribeMessage<mdl_bar_msg::CrudeFutureSecondBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_ThemeMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::ThemeMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_SHSZAccuMoneyFlow:
                    sub->SubcribeMessage<mdl_bar_msg::SHSZAccuMoneyFlow>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_SHFEOptionMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::SHFEOptionMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_SHFEOptionMultiMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::SHFEOptionMultiMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_SHFEOptionBar:
                    sub->SubcribeMessage<mdl_bar_msg::SHFEOptionBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_IndexStatistics:
                    sub->SubcribeMessage<mdl_bar_msg::IndexStatistics>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_SwgIndexMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::SwgIndexMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_DYThemeIndexAA:
                    sub->SubcribeMessage<mdl_bar_msg::DYThemeIndexAA>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_DYThemeMoneyFlow:
                    sub->SubcribeMessage<mdl_bar_msg::DYThemeMoneyFlow>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_CustomTheme:
                    sub->SubcribeMessage<mdl_bar_msg::CustomTheme>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_SHSZDeltaPrice:
                    sub->SubcribeMessage<mdl_bar_msg::SHSZDeltaPrice>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_AHComp:
                    sub->SubcribeMessage<mdl_bar_msg::AHComp>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_SHSZAccuMoneyFlow2:
                    sub->SubcribeMessage<mdl_bar_msg::SHSZAccuMoneyFlow2>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_SHSZAccuMoneyFlow2Ext:
                    sub->SubcribeMessage<mdl_bar_msg::SHSZAccuMoneyFlow2Ext>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_HKEXIndexMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::HKEXIndexMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_MarketMoneyFlow:
                    sub->SubcribeMessage<mdl_bar_msg::MarketMoneyFlow>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_PreMktLimitChange:
                    sub->SubcribeMessage<mdl_bar_msg::PreMktLimitChange>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_SHSZDynamicMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::SHSZDynamicMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_CFFEXOptionBar:
                    sub->SubcribeMessage<mdl_bar_msg::CFFEXOptionBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_XSHEOptionBar:
                    sub->SubcribeMessage<mdl_bar_msg::XSHEOptionBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_CniIndexMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::CniIndexMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_SHSZAccuMoneyFlow2Extra:
                    sub->SubcribeMessage<mdl_bar_msg::SHSZAccuMoneyFlow2Extra>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_SHSZMoneyFlow2PriceSpread:
                    sub->SubcribeMessage<mdl_bar_msg::SHSZMoneyFlow2PriceSpread>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_DYThemeIndexExtra:
                    sub->SubcribeMessage<mdl_bar_msg::DYThemeIndexExtra>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_RegionMoneyFlow:
                    sub->SubcribeMessage<mdl_bar_msg::RegionMoneyFlow>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_CsiIndexMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::CsiIndexMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_DeltaIndex:
                    sub->SubcribeMessage<mdl_bar_msg::DeltaIndex>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_OrderDetailStat:
                    sub->SubcribeMessage<mdl_bar_msg::OrderDetailStat>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_SHSZLimitStatus:
                    sub->SubcribeMessage<mdl_bar_msg::SHSZLimitStatus>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_IndexDynamicBar:
                    sub->SubcribeMessage<mdl_bar_msg::IndexDynamicBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_MarketMetrics:
                    sub->SubcribeMessage<mdl_bar_msg::MarketMetrics>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_DeltaPTFPTick:
                    sub->SubcribeMessage<mdl_bar_msg::DeltaPTFPTick>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_IndexMoneyFlow2:
                    sub->SubcribeMessage<mdl_bar_msg::IndexMoneyFlow2>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_FundsValuation:
                    sub->SubcribeMessage<mdl_bar_msg::FundsValuation>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_CrudeOptionMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::CrudeOptionMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_SWIndustry2:
                    sub->SubcribeMessage<mdl_bar_msg::SWIndustry2>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_XSHEBondMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::XSHEBondMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_XSHGBondMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::XSHGBondMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_DYThemeIndex2:
                    sub->SubcribeMessage<mdl_bar_msg::DYThemeIndex2>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_GFEXFutureMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::GFEXFutureMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_GFEXOptionMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::GFEXOptionMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_ThemeMinuteBar2:
                    sub->SubcribeMessage<mdl_bar_msg::ThemeMinuteBar2>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_SHSZDeltaIndex:
                    sub->SubcribeMessage<mdl_bar_msg::SHSZDeltaIndex>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_DYThemeIndex3:
                    sub->SubcribeMessage<mdl_bar_msg::DYThemeIndex3>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_ThemeMinuteBar3:
                    sub->SubcribeMessage<mdl_bar_msg::ThemeMinuteBar3>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_BJBondBar:
                    sub->SubcribeMessage<mdl_bar_msg::BJBondBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_DYThemeMoneyFlow3:
                    sub->SubcribeMessage<mdl_bar_msg::DYThemeMoneyFlow3>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_TickerChgPct:
                    sub->SubcribeMessage<mdl_bar_msg::TickerChgPct>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_BJStockMinuteBar:
                    sub->SubcribeMessage<mdl_bar_msg::BJStockMinuteBar>();
                    break;
                case mdl_bar_msg::MDLMID_MDL_BAR_ETFMoneyFlow:
                    sub->SubcribeMessage<mdl_bar_msg::ETFMoneyFlow>();
                    break;
                default:
                    break;
            }
        }

        for (const auto &SHNYMsgID: mdl_conf.SHNY) {
            std::cout << "subscribe shny msg id: " << SHNYMsgID << std::endl;
            switch (SHNYMsgID) {
                case mdl_shny_msg::MDLMID_MDL_SHNY_CrudeFuture:
                    sub->SubcribeMessage<mdl_shny_msg::CrudeFuture>();
                    break;
                case mdl_shny_msg::MDLMID_MDL_SHNY_FuturePlus:
                    sub->SubcribeMessage<mdl_shny_msg::FuturePlus>();
                    break;
                case mdl_shny_msg::MDLMID_MDL_SHNY_CrudeOption:
                    sub->SubcribeMessage<mdl_shny_msg::CrudeOption>();
                    break;
                default:
                    break;
            }
        }

        for (const auto &DIGICURMsgID: mdl_conf.DigiCur) {
            std::cout << "subscribe digicur msg id: " << DIGICURMsgID << std::endl;
            switch (DIGICURMsgID) {
                case mdl_digicur_msg::MDLMID_MDL_DIGICUR_GeminiAuction:
                    sub->SubcribeMessage<mdl_digicur_msg::GeminiAuction>();
                    break;
                case mdl_digicur_msg::MDLMID_MDL_DIGICUR_GeminiOrder:
                    sub->SubcribeMessage<mdl_digicur_msg::GeminiOrder>();
                    break;
                case mdl_digicur_msg::MDLMID_MDL_DIGICUR_GeminiTick:
                    sub->SubcribeMessage<mdl_digicur_msg::GeminiTick>();
                    break;
                case mdl_digicur_msg::MDLMID_MDL_DIGICUR_CoinMarketCapTick:
                    sub->SubcribeMessage<mdl_digicur_msg::CoinMarketCapTick>();
                    break;
                case mdl_digicur_msg::MDLMID_MDL_DIGICUR_CoinMarketCapGlobal:
                    sub->SubcribeMessage<mdl_digicur_msg::CoinMarketCapGlobal>();
                    break;
                case mdl_digicur_msg::MDLMID_MDL_DIGICUR_KrakenOrder:
                    sub->SubcribeMessage<mdl_digicur_msg::KrakenOrder>();
                    break;
                case mdl_digicur_msg::MDLMID_MDL_DIGICUR_KrakenTick:
                    sub->SubcribeMessage<mdl_digicur_msg::KrakenTick>();
                    break;
                case mdl_digicur_msg::MDLMID_MDL_DIGICUR_KrakenTrans:
                    sub->SubcribeMessage<mdl_digicur_msg::KrakenTrans>();
                    break;
                case mdl_digicur_msg::MDLMID_MDL_DIGICUR_BitstampFullOrder:
                    sub->SubcribeMessage<mdl_digicur_msg::BitstampFullOrder>();
                    break;
                case mdl_digicur_msg::MDLMID_MDL_DIGICUR_BitstampTick:
                    sub->SubcribeMessage<mdl_digicur_msg::BitstampTick>();
                    break;
                case mdl_digicur_msg::MDLMID_MDL_DIGICUR_BitstampTrans:
                    sub->SubcribeMessage<mdl_digicur_msg::BitstampTrans>();
                    break;
                case mdl_digicur_msg::MDLMID_MDL_DIGICUR_GdaxLevel2Order:
                    sub->SubcribeMessage<mdl_digicur_msg::GdaxLevel2Order>();
                    break;
                case mdl_digicur_msg::MDLMID_MDL_DIGICUR_GdaxTick:
                    sub->SubcribeMessage<mdl_digicur_msg::GdaxTick>();
                    break;
                case mdl_digicur_msg::MDLMID_MDL_DIGICUR_GdaxTrans:
                    sub->SubcribeMessage<mdl_digicur_msg::GdaxTrans>();
                    break;
                case mdl_digicur_msg::MDLMID_MDL_DIGICUR_ItbitOrder:
                    sub->SubcribeMessage<mdl_digicur_msg::ItbitOrder>();
                    break;
                case mdl_digicur_msg::MDLMID_MDL_DIGICUR_ItbitTick:
                    sub->SubcribeMessage<mdl_digicur_msg::ItbitTick>();
                    break;
                case mdl_digicur_msg::MDLMID_MDL_DIGICUR_ItbitTrans:
                    sub->SubcribeMessage<mdl_digicur_msg::ItbitTrans>();
                    break;
                case mdl_digicur_msg::MDLMID_MDL_DIGICUR_CryFacilitiesOrder:
                    sub->SubcribeMessage<mdl_digicur_msg::CryFacilitiesOrder>();
                    break;
                case mdl_digicur_msg::MDLMID_MDL_DIGICUR_CryFacilitiesTick:
                    sub->SubcribeMessage<mdl_digicur_msg::CryFacilitiesTick>();
                    break;
                default:
                    break;
            }
        }

        for (const auto &CSIMsgID: mdl_conf.CSI) {
            std::cout << "subscribe csi msg id: " << CSIMsgID << std::endl;
            switch (CSIMsgID) {
                case mdl_csi_msg::MDLMID_MDL_CSI_CSIndex:
                    sub->SubcribeMessage<mdl_csi_msg::CSIndex>();
                    break;
                case mdl_csi_msg::MDLMID_MDL_CSI_EtfIopv:
                    sub->SubcribeMessage<mdl_csi_msg::EtfIopv>();
                    break;
                default:
                    break;
            }
        }

        for (const auto &CNIMsgID: mdl_conf.CNI) {
            std::cout << "subscribe cni msg id: " << CNIMsgID << std::endl;
            switch (CNIMsgID) {
                case mdl_cni_msg::MDLMID_MDL_CNI_Snapshot360001:
                    sub->SubcribeMessage<mdl_cni_msg::Snapshot360001>();
                    break;
                default:
                    break;
            }
        }

        // connect to server
        std::string err = sub->Connect();
        if (err.empty()) {
            printf("Connect to server successfully.\n");
            return true;
        }
        printf("Connect to server failed: %s.\n", err.c_str());
        return false;
    }

    void Close() {
        if (!m_IOManager.IsNull()) {
            m_IOManager->Shutdown();
        }
    }

    // handle network failure
    virtual void OnMDLAPIMessage(const MDLMessage *msg) {
        MDLMessageHead *head = msg->GetHead();
        if (head->MessageID == mdl_api_msg::ConnectingEvent::MessageID) {
            auto *resp = (mdl_api_msg::ConnectingEvent *) msg->GetBody();
            printf("Connect to %s ...\n", resp->Address.c_str());
        } else if (head->MessageID == mdl_api_msg::ConnectErrorEvent::MessageID) {
            auto *resp = (mdl_api_msg::ConnectErrorEvent *) msg->GetBody();
            printf("Connect to %s failed %s.\n", resp->Address.c_str(), resp->ErrorMessage.c_str());
        } else if (head->MessageID == mdl_api_msg::DisconnectedEvent::MessageID) {
            auto *resp = (mdl_api_msg::DisconnectedEvent *) msg->GetBody();
            printf("Disconnected from %s: %s.\n", resp->Address.c_str(), resp->ErrorMessage.c_str());
        }
    }

    // handle server response
    virtual void OnMDLSysMessage(const MDLMessage *msg) {
        MDLMessageHead *head = msg->GetHead();
        if (head->MessageID == mdl_sys_msg::LogonResponse::MessageID) {
            mdl_sys_msg::LogonResponse *resp = (mdl_sys_msg::LogonResponse *) msg->GetBody();
            if (resp->ReturnCode != MDLEC_OK) {
                printf("Logon failed: return code %d.\n", resp->ReturnCode);
            }
            for (uint32_t i = 0; i < resp->Services.Length; ++i) {
                for (uint32_t j = 0; j < resp->Services[i]->Messages.Length; ++j) {
                    if (resp->Services[i]->Messages[j]->MessageStatus != MDLEC_OK) {
                        printf("The server doesn't publish message (service id %d message id %d)\n",
                               resp->Services[i]->ServiceID,
                               resp->Services[i]->Messages[j]->MessageID);
                    }
                }
            }
        }
    }

    // print shfe future message
    virtual void OnMDLSHFEMessage(const MDLMessage *msg) {
        MDLMessageHead *head = msg->GetHead();
        if (head->MessageID == mdl_shfe_msg::CTPFuture::MessageID) {
            shfe_msg.ctpFuture.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shfe_msg::Future::MessageID) {
            shfe_msg.future.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shfe_msg::FuturePlus::MessageID) {
            shfe_msg.futurePlus.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shfe_msg::CTPOption::MessageID) {
            shfe_msg.ctpOption.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shfe_msg::Index::MessageID) {
            shfe_msg.index.emplace_back(msg->Copy());
        }
        m_OnMessageCallback(SubID(MDLSID_MDL_SHFE, head->MessageID), msg);
    }

    virtual void OnMDLSHFEL2Message(const MDLMessage *msg) {
        MDLMessageHead *head = msg->GetHead();
        if (head->MessageID == mdl_shfel2_msg::CTPFuture::MessageID) {
            shfel2_msg.ctpFuture.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shfel2_msg::CTPOption::MessageID) {
            shfel2_msg.ctpOption.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shfel2_msg::CrudeFuture::MessageID) {
            shfel2_msg.crudeFuture.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shfel2_msg::CrudeOption::MessageID) {
            shfel2_msg.crudeOption.emplace_back(msg->Copy());
        }
        m_OnMessageCallback(SubID(MDLSID_MDL_SHFEL2, head->MessageID), msg);
    }

    // print czce future message
    virtual void OnMDLCZCEMessage(const MDLMessage *msg) {
        MDLMessageHead *head = msg->GetHead();
        if (head->MessageID == mdl_czce_msg::CTPFuture::MessageID) {
            czce_msg.ctpFuture.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_czce_msg::Future::MessageID) {
            czce_msg.future.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_czce_msg::CZCEFuture::MessageID) {
            czce_msg.czceFuture.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_czce_msg::Level2Future::MessageID) {
            czce_msg.level2Future.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_czce_msg::FuturePlus::MessageID) {
            czce_msg.futurePlus.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_czce_msg::CTPOption::MessageID) {
            czce_msg.ctpOption.emplace_back(msg->Copy());
        }
        m_OnMessageCallback(SubID(MDLSID_MDL_CZCE, head->MessageID), msg);
    }

    // print cffex future message
    virtual void OnMDLCFFEXMessage(const MDLMessage *msg) {
        MDLMessageHead *head = msg->GetHead();
        if (head->MessageID == mdl_cffex_msg::CTPFuture::MessageID) {
            cffex_msg.ctpFuture.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_cffex_msg::Future::MessageID) {
            cffex_msg.future.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_cffex_msg::FuturePlus::MessageID) {
            cffex_msg.futurePlus.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_cffex_msg::Option::MessageID) {
            cffex_msg.option.emplace_back(msg->Copy());
        }
        m_OnMessageCallback(SubID(MDLSID_MDL_CFFEX, head->MessageID), msg);
    }

    virtual void OnMDLCFFEXL2Message(const MDLMessage *msg) {
        MDLMessageHead *head = msg->GetHead();
        if (head->MessageID == mdl_cffexl2_msg::Future::MessageID) {
            cffexl2_msg.future.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_cffexl2_msg::Option::MessageID) {
            cffexl2_msg.option.emplace_back(msg->Copy());
        }
        m_OnMessageCallback(SubID(MDLSID_MDL_CFFEXL2, head->MessageID), msg);
    }

    // print cffex2 future message
    virtual void OnMDLCZCEL2Message(const MDLMessage *msg) {
        MDLMessageHead *head = msg->GetHead();
        if (head->MessageID == mdl_czcel2_msg::CTPFuture::MessageID) {
            czcel2_msg.ctpFuture.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_czcel2_msg::CTPOption::MessageID) {
            czcel2_msg.ctpOption.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_czcel2_msg::FutureTrdQtyStatus::MessageID) {
            czcel2_msg.futureTrdQtyStatus.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_czcel2_msg::OptionTrdQtyStatus::MessageID) {
            czcel2_msg.optionTrdQtyStatus.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_czcel2_msg::CmbTypeInfo::MessageID) {
            czcel2_msg.cmbTypeInfo.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_czcel2_msg::OptionParam::MessageID) {
            czcel2_msg.optionParam.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_czcel2_msg::TradeStatus::MessageID) {
            czcel2_msg.tradeStatus.emplace_back(msg->Copy());
        }
        m_OnMessageCallback(SubID(MDLSID_MDL_CZCEL2, head->MessageID), msg);
    }

    // print dce future message
    virtual void OnMDLDCEMessage(const MDLMessage *msg) {
        MDLMessageHead *head = msg->GetHead();
        if (head->MessageID == mdl_dce_msg::CTPFuture::MessageID) {
            dce_msg.ctpFuture.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_dce_msg::Future::MessageID) {
            dce_msg.future.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_dce_msg::FuturePlus::MessageID) {
            dce_msg.futurePlus.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_dce_msg::CTPOption::MessageID) {
            dce_msg.ctpOption.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_dce_msg::Indexes::MessageID) {
            dce_msg.indexes.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_dce_msg::Index2::MessageID) {
            dce_msg.index2.emplace_back(msg->Copy());
        }
        m_OnMessageCallback(SubID(MDLSID_MDL_DCE, head->MessageID), msg);
    }

    virtual void OnMDLDCEL2Message(const MDLMessage *msg) {
        MDLMessageHead *head = msg->GetHead();
        if (head->MessageID == mdl_dcel2_msg::Future::MessageID) {
            dcel2_msg.future.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_dcel2_msg::Option::MessageID) {
            dcel2_msg.option.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_dcel2_msg::FutureTrdQty::MessageID) {
            dcel2_msg.futureTrdQty.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_dcel2_msg::OptionTrdQty::MessageID) {
            dcel2_msg.optionTrdQty.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_dcel2_msg::CmbTypeInfo::MessageID) {
            dcel2_msg.cmbTypeInfo.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_dcel2_msg::OptionParam::MessageID) {
            dcel2_msg.optionParam.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_dcel2_msg::FutureOrder::MessageID) {
            dcel2_msg.futureOrder.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_dcel2_msg::OptionOrder::MessageID) {
            dcel2_msg.optionOrder.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_dcel2_msg::CmbOrder::MessageID) {
            dcel2_msg.cmbOrder.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_dcel2_msg::OrderStat::MessageID) {
            dcel2_msg.orderStat.emplace_back(msg->Copy());
        }
        m_OnMessageCallback(SubID(MDLSID_MDL_DCEL2, head->MessageID), msg);
    }

    // print shanghai level1 message
    virtual void OnMDLSHL1Message(const MDLMessage *msg) {
        MDLMessageHead *head = msg->GetHead();
        if (head->MessageID == mdl_shl1_msg::SHL1Index::MessageID) {
            sh1_msg.shl1Index.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl1_msg::SHL1Stock::MessageID) {
            sh1_msg.shl1Stock.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl1_msg::Indexes::MessageID) {
            sh1_msg.indexes.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl1_msg::Equity::MessageID) {
            sh1_msg.equity.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl1_msg::Bonds::MessageID) {
            sh1_msg.bonds.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl1_msg::Funds::MessageID) {
            sh1_msg.funds.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl1_msg::Level1Plus::MessageID) {
            sh1_msg.level1Plus.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl1_msg::Equity2::MessageID) {
            sh1_msg.equity2.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl1_msg::Bond2::MessageID) {
            sh1_msg.bond2.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl1_msg::Fund2::MessageID) {
            sh1_msg.fund2.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl1_msg::Comp::MessageID) {
            sh1_msg.comp.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl1_msg::BondDist::MessageID) {
            sh1_msg.bondDist.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl1_msg::ATP::MessageID) {
            sh1_msg.atp.emplace_back(msg->Copy());
        }
        m_OnMessageCallback(SubID(MDLSID_SHL1, head->MessageID), msg);
    }

    // print shenzhen level1 message
    virtual void OnMDLSZL1Message(const MDLMessage *msg) {
        MDLMessageHead *head = msg->GetHead();
        if (head->MessageID == mdl_szl1_msg::SZL1Index::MessageID) {
            sz1_msg.szl1Index.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl1_msg::SZL1Stock::MessageID) {
            sz1_msg.szl1Stock.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl1_msg::Level1Plus::MessageID) {
            sz1_msg.level1Plus.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl1_msg::SZL1Option::MessageID) {
            sz1_msg.szl1Option.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl1_msg::SZL1Option2::MessageID) {
            sz1_msg.szl1Option2.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl1_msg::Bonds::MessageID) {
            sz1_msg.bonds.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl1_msg::Order300392::MessageID) {
            sz1_msg.order300392.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl1_msg::Transaction300391::MessageID) {
            sz1_msg.transaction300391.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl1_msg::BondDistribution::MessageID) {
            sz1_msg.bondDistribution.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl1_msg::ATP::MessageID) {
            sz1_msg.atp.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl1_msg::Index2::MessageID) {
            sz1_msg.index2.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl1_msg::Snapshot309211::MessageID) {
            sz1_msg.snapshot309211.emplace_back(msg->Copy());
        }
        m_OnMessageCallback(SubID(MDLSID_SZL1, head->MessageID), msg);
    }

    // print hkex message
    virtual void OnMDLHKEXMessage(const MDLMessage *msg) {
        MDLMessageHead *head = msg->GetHead();
        if (head->MessageID == mdl_hkex_msg::OMDMarketData::MessageID) {
            hkex_msg.omdMarketData.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_hkex_msg::HangSengIndex::MessageID) {
            hkex_msg.hangSengIndex.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_hkex_msg::OMDClosingPrice::MessageID) {
            hkex_msg.omdClosingPrice.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_hkex_msg::SCMarketTurnover::MessageID) {
            hkex_msg.scMarketTurnover.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_hkex_msg::SCDailyQuotaBalance::MessageID) {
            hkex_msg.scDailyQuotaBalance.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_hkex_msg::OMDIndexData::MessageID) {
            hkex_msg.omdIndexData.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_hkex_msg::OMDSecurityDefinition::MessageID) {
            hkex_msg.omdSecurityDefinition.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_hkex_msg::OMDTradeTicker::MessageID) {
            hkex_msg.omdTradeTicker.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_hkex_msg::BrokerQueue::MessageID) {
            hkex_msg.brokerQueue.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_hkex_msg::OMDOrderBook::MessageID) {
            hkex_msg.omdOrderBook.emplace_back(msg->Copy());
        }
        m_OnMessageCallback(SubID(MDLSID_MDL_HKEX, head->MessageID), msg);
    }

    // handle shenzhen level2 message
    virtual void OnMDLSZL2Message(const MDLMessage *msg) {
        MDLMessageHead *head = msg->GetHead();
        if (head->MessageID == mdl_szl2_msg::Trade::MessageID) {
            sz2_msg.trade.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Order::MessageID) {
            sz2_msg.order.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Index::MessageID) {
            sz2_msg.index.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::MarketData::MessageID) {
            sz2_msg.marketData.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::StockStatus::MessageID) {
            sz2_msg.stockStatus.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::StockInfo::MessageID) {
            sz2_msg.stockInfo.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::SysParam::MessageID) {
            sz2_msg.sysParam.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Snapshot300111::MessageID) {
            sz2_msg.snapshot300111.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Snapshot309011::MessageID) {
            sz2_msg.snapshot309011.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Snapshot309111::MessageID) {
            sz2_msg.snapshot309111.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Snapshot300611::MessageID) {
            sz2_msg.snapshot300611.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::SecurityStatus::MessageID) {
            sz2_msg.securityStatus.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Order300192::MessageID) {
            sz2_msg.order300192.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Order300592::MessageID) {
            sz2_msg.order300592.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Order300792::MessageID) {
            sz2_msg.order300792.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Transaction300191::MessageID) {
            sz2_msg.transaction300191.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Transaction300591::MessageID) {
            sz2_msg.transaction300591.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Transaction300791::MessageID) {
            sz2_msg.transaction300791.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Bulletin::MessageID) {
            sz2_msg.bulletin.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Snapshot300111_v2::MessageID) {
            sz2_msg.snapshot300111_v2.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Snapshot309011_v2::MessageID) {
            sz2_msg.snapshot309011_v2.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Snapshot309111_v2::MessageID) {
            sz2_msg.snapshot309111_v2.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Snapshot300611_v2::MessageID) {
            sz2_msg.snapshot300611_v2.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Order300192_v2::MessageID) {
            sz2_msg.order300192_v2.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Order300592_v2::MessageID) {
            sz2_msg.order300592_v2.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Order300792_v2::MessageID) {
            sz2_msg.order300792_v2.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Transaction300191_v2::MessageID) {
            sz2_msg.transaction300191_v2.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Transaction300591_v2::MessageID) {
            sz2_msg.transaction300591_v2.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Transaction300791_v2::MessageID) {
            sz2_msg.transaction300791_v2.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Snapshot300111_v3::MessageID) {
            sz2_msg.snapshot300111_v3.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::RealQuota::MessageID) {
            sz2_msg.realQuota.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Snapshot306311::MessageID) {
            sz2_msg.snapshot306311.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Snapshot300211::MessageID) {
            sz2_msg.snapshot300211.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Order300292::MessageID) {
            sz2_msg.order300292.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Transaction300291::MessageID) {
            sz2_msg.transaction300291.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Order300392::MessageID) {
            sz2_msg.order300392.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Transaction300391::MessageID) {
            sz2_msg.transaction300391.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::BondOrderQueue::MessageID) {
            sz2_msg.bondOrderQueue.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::BondDistribution::MessageID) {
            sz2_msg.bondDistribution.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::Snapshot309211::MessageID) {
            sz2_msg.snapshot309211.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::ETFData::MessageID) {
            sz2_msg.etfData.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_szl2_msg::MarketInfo::MessageID) {
            sz2_msg.marketInfo.emplace_back(msg->Copy());
        }
        m_OnMessageCallback(SubID(MDLSID_SZL2, head->MessageID), msg);
    }

    virtual void OnMDLGFEXMessage(const MDLMessage *msg) {
        MDLMessageHead *head = msg->GetHead();
        if (head->MessageID == mdl_gfex_msg::Future::MessageID) {
            gfex_msg.future.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_gfex_msg::Option::MessageID) {
            gfex_msg.option.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_gfex_msg::FuturePlus::MessageID) {
            gfex_msg.futurePlus.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_gfex_msg::CmbTypeInfo::MessageID) {
            gfex_msg.cmbTypeInfo.emplace_back(msg->Copy());
        }
        m_OnMessageCallback(SubID(MDLSID_MDL_GFEX, head->MessageID), msg);
    }

    virtual void OnMDLGFEXL2Message(const MDLMessage *msg) {
        MDLMessageHead *head = msg->GetHead();
        if (head->MessageID == mdl_gfexl2_msg::Future::MessageID) {
            gfexl2_msg.future.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_gfexl2_msg::Option::MessageID) {
            gfexl2_msg.option.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_gfexl2_msg::FutureTrdQty::MessageID) {
            gfexl2_msg.futureTrdQty.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_gfexl2_msg::OptionTrdQty::MessageID) {
            gfexl2_msg.optionTrdQty.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_gfexl2_msg::CmbTypeInfo::MessageID) {
            gfexl2_msg.cmbTypeInfo.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_gfexl2_msg::OptionParam::MessageID) {
            gfexl2_msg.optionParam.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_gfexl2_msg::FutureOrder::MessageID) {
            gfexl2_msg.futureOrder.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_gfexl2_msg::OptionOrder::MessageID) {
            gfexl2_msg.optionOrder.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_gfexl2_msg::CmbOrder::MessageID) {
            gfexl2_msg.cmbOrder.emplace_back(msg->Copy());
        }
        m_OnMessageCallback(SubID(MDLSID_MDL_GFEXL2, head->MessageID), msg);
    }

    virtual void OnMDLSHL2Message(const MDLMessage *msg) {
        MDLMessageHead *head = msg->GetHead();
        if (head->MessageID == mdl_shl2_msg::SHL1Stock::MessageID) {
            sh2_msg.shl1Stock.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::SHL2Transaction::MessageID) {
            sh2_msg.shl2Transaction.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::SHL2MarketData::MessageID) {
            sh2_msg.shl2MarketData.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::SHL2VirtualAuctionPrice::MessageID) {
            sh2_msg.shl2VirtualAuctionPrice.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::SHL2Index::MessageID) {
            sh2_msg.shl2Index.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::SHL2MarketOverview::MessageID) {
            sh2_msg.shl2MarketOverview.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::SHL2Statics::MessageID) {
            sh2_msg.shl2Statics.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::OPTLevel1::MessageID) {
            sh2_msg.optLevel1.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::MDSnapshotFullRefresh_4001::MessageID) {
            sh2_msg.mdSnapshotFullRefresh_4001.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::FixedIncomePrice::MessageID) {
            sh2_msg.fixedIncomePrice.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::FixedIncomeInfo::MessageID) {
            sh2_msg.fixedIncomeInfo.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::FixedIncomeTransaction::MessageID) {
            sh2_msg.fixedIncomeTransaction.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::FixedIncomeFirmQuote::MessageID) {
            sh2_msg.fixedIncomeFirmQuote.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::RealQuota::MessageID) {
            sh2_msg.realQuota.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::ATPMarketData::MessageID) {
            sh2_msg.atpMarketData.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::ATPTransaction::MessageID) {
            sh2_msg.atpTransaction.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::SHL2Transaction2::MessageID) {
            sh2_msg.shl2Transaction2.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::Order::MessageID) {
            sh2_msg.order.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::BondMarketData::MessageID) {
            sh2_msg.bondMarketData.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::BondTick::MessageID) {
            sh2_msg.bondTick.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::BondOrderQueue::MessageID) {
            sh2_msg.bondOrderQueue.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::MarketInfo::MessageID) {
            sh2_msg.marketInfo.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::NGTSTick::MessageID) {
            sh2_msg.ngtsTick.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::ETFMarketData::MessageID) {
            sh2_msg.etfMarketData.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::BondDist::MessageID) {
            sh2_msg.bondDist.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::BondDistOrderQueue::MessageID) {
            sh2_msg.bondDistOrderQueue.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_shl2_msg::BondDistTick::MessageID) {
            sh2_msg.bondDistTick.emplace_back(msg->Copy());
        }
        m_OnMessageCallback(SubID(MDLSID_SHL2, head->MessageID), msg);
    }

    virtual void OnMDLSWGMessage(const MDLMessage *msg) {
        MDLMessageHead *head = msg->GetHead();
        if (head->MessageID == mdl_swg_msg::Index::MessageID) {
            swg_msg.index.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_swg_msg::HuobiMarketData::MessageID) {
            swg_msg.huobiMarketData.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_swg_msg::HuobiTrade::MessageID) {
            swg_msg.huobiTrade.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_swg_msg::HuobiOrder::MessageID) {
            swg_msg.huobiOrder.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_swg_msg::HuobiMinuteBar::MessageID) {
            swg_msg.huobiMinuteBar.emplace_back(msg->Copy());
        }
        m_OnMessageCallback(SubID(MDLSID_MDL_SWG, head->MessageID), msg);
    }

    virtual void OnMDLNEEQMessage(const MDLMessage *msg) {
        MDLMessageHead *head = msg->GetHead();
        if (head->MessageID == mdl_neeq_msg::Index::MessageID) {
            neeq_msg.index.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_neeq_msg::Stock::MessageID) {
            neeq_msg.stock.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_neeq_msg::MatchedBargainOrder::MessageID) {
            neeq_msg.matchedBargainOrder.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_neeq_msg::SecurityStatus::MessageID) {
            neeq_msg.securityStatus.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_neeq_msg::NanHuaFuture::MessageID) {
            neeq_msg.nanHuaFuture.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_neeq_msg::FXCMForex::MessageID) {
            neeq_msg.fxcmForex.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_neeq_msg::BJStock::MessageID) {
            neeq_msg.bjStock.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_neeq_msg::BJBondSnapshot::MessageID) {
            neeq_msg.bjBondSnapshot.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_neeq_msg::BJBondOrder011::MessageID) {
            neeq_msg.bjBondOrder011.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_neeq_msg::BJBondTrans01X::MessageID) {
            neeq_msg.bjBondTrans01X.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_neeq_msg::BJBondSnapshotL1::MessageID) {
            neeq_msg.bjBondSnapshotL1.emplace_back(msg->Copy());
        }
        m_OnMessageCallback(SubID(MDLSID_MDL_NEEQ, head->MessageID), msg);
    }

    virtual void OnMDLDIGICURMessage(const MDLMessage *msg) {
        MDLMessageHead *head = msg->GetHead();
        if (head->MessageID == mdl_digicur_msg::GeminiAuction::MessageID) {
            digicur_msg.geminiAuction.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_digicur_msg::GeminiOrder::MessageID) {
            digicur_msg.geminiOrder.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_digicur_msg::GeminiTick::MessageID) {
            digicur_msg.geminiTick.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_digicur_msg::CoinMarketCapTick::MessageID) {
            digicur_msg.coinMarketCapTick.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_digicur_msg::CoinMarketCapGlobal::MessageID) {
            digicur_msg.coinMarketCapGlobal.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_digicur_msg::KrakenOrder::MessageID) {
            digicur_msg.krakenOrder.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_digicur_msg::KrakenTick::MessageID) {
            digicur_msg.krakenTick.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_digicur_msg::KrakenTrans::MessageID) {
            digicur_msg.krakenTrans.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_digicur_msg::BitstampFullOrder::MessageID) {
            digicur_msg.bitstampFullOrder.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_digicur_msg::BitstampTick::MessageID) {
            digicur_msg.bitstampTick.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_digicur_msg::BitstampTrans::MessageID) {
            digicur_msg.bitstampTrans.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_digicur_msg::GdaxLevel2Order::MessageID) {
            digicur_msg.gdaxLevel2Order.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_digicur_msg::GdaxTick::MessageID) {
            digicur_msg.gdaxTick.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_digicur_msg::GdaxTrans::MessageID) {
            digicur_msg.gdaxTrans.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_digicur_msg::ItbitOrder::MessageID) {
            digicur_msg.itbitOrder.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_digicur_msg::ItbitTick::MessageID) {
            digicur_msg.itbitTick.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_digicur_msg::ItbitTrans::MessageID) {
            digicur_msg.itbitTrans.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_digicur_msg::CryFacilitiesOrder::MessageID) {
            digicur_msg.cryFacilitiesOrder.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_digicur_msg::CryFacilitiesTick::MessageID) {
            digicur_msg.cryFacilitiesTick.emplace_back(msg->Copy());
        }
        m_OnMessageCallback(SubID(MDLSID_MDL_DIGICUR, head->MessageID), msg);
    }

    virtual void OnMDLCSIMessage(const MDLMessage *msg) {
        MDLMessageHead *head = msg->GetHead();
        if (head->MessageID == mdl_csi_msg::CSIndex::MessageID) {
            csi_msg.csIndex.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_csi_msg::EtfIopv::MessageID) {
            csi_msg.etfIopv.emplace_back(msg->Copy());
        }
        m_OnMessageCallback(SubID(MDLSID_MDL_CSI, head->MessageID), msg);
    }

    virtual void OnMDLCNIMessage(const MDLMessage *msg) {
        MDLMessageHead *head = msg->GetHead();
        if (head->MessageID == mdl_cni_msg::Snapshot360001::MessageID) {
            cni_msg.snapshot360001.emplace_back(msg->Copy());
        }
        m_OnMessageCallback(SubID(MDLSID_MDL_CNI, head->MessageID), msg);
    }

    virtual void OnMDLBARMessage(const MDLMessage *msg) {
        MDLMessageHead *head = msg->GetHead();
        if (head->MessageID == mdl_bar_msg::XSHGStockMinuteBar::MessageID) {
            bar_msg.xshgStockMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::XSHEStockMinuteBar::MessageID) {
            bar_msg.xsheStockMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::HKEXStockMinuteBar::MessageID) {
            bar_msg.hkexStockMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::DCEFutureMinuteBar::MessageID) {
            bar_msg.dceFutureMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::SHFEFutureMinuteBar::MessageID) {
            bar_msg.shfeFutureMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::CZCEFutureMinuteBar::MessageID) {
            bar_msg.czceFutureMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::CFFEXFutureMinuteBar::MessageID) {
            bar_msg.cffexFutureMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::XSHGOptionMinuteBar::MessageID) {
            bar_msg.xshgOptionMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::XSHGIndexMinuteBar::MessageID) {
            bar_msg.xshgIndexMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::XSHEIndexMinuteBar::MessageID) {
            bar_msg.xsheIndexMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::XSHGCapitalFlow::MessageID) {
            bar_msg.xshgCapitalFlow.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::XSHECapitalFlow::MessageID) {
            bar_msg.xsheCapitalFlow.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::IndustryCapitalFlow::MessageID) {
            bar_msg.industryCapitalFlow.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::XSHGMoneyFlow::MessageID) {
            bar_msg.xshgMoneyFlow.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::XSHEMoneyFlow::MessageID) {
            bar_msg.xsheMoneyFlow.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::XSHGStockMultiMinuteBar::MessageID) {
            bar_msg.xshgStockMultiMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::XSHEStockMultiMinuteBar::MessageID) {
            bar_msg.xsheStockMultiMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::HKEXStockMultiMinuteBar::MessageID) {
            bar_msg.hkexStockMultiMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::DCEFutureMultiMinuteBar::MessageID) {
            bar_msg.dceFutureMultiMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::SHFEFutureMultiMinuteBar::MessageID) {
            bar_msg.shfeFutureMultiMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::CZCEFutureMultiMinuteBar::MessageID) {
            bar_msg.czceFutureMultiMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::CFFEXFutureMultiMinuteBar::MessageID) {
            bar_msg.cffexFutureMultiMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::XSHGOptionMultiMinuteBar::MessageID) {
            bar_msg.xshgOptionMultiMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::XSHGIndexMultiMinuteBar::MessageID) {
            bar_msg.xshgIndexMultiMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::XSHEIndexMultiMinuteBar::MessageID) {
            bar_msg.xsheIndexMultiMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::IndustryMoneyFlow::MessageID) {
            bar_msg.industryMoneyFlow.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::HangSengIndexMinuteBar::MessageID) {
            bar_msg.hangSengIndexMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::DeltaTick::MessageID) {
            bar_msg.deltaTick.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::NeeqMinuteBar::MessageID) {
            bar_msg.neeqMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::NeeqMultiMinuteBar::MessageID) {
            bar_msg.neeqMultiMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::DCEFutureBar::MessageID) {
            bar_msg.dceFutureBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::SHFEFutureBar::MessageID) {
            bar_msg.shfeFutureBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::CZCEFutureBar::MessageID) {
            bar_msg.czceFutureBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::CFFEXFutureBar::MessageID) {
            bar_msg.cffexFutureBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::CZCEOptionMinuteBar::MessageID) {
            bar_msg.czceOptionMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::CZCEOptionMultiMinuteBar::MessageID) {
            bar_msg.czceOptionMultiMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::DCEOptionMinuteBar::MessageID) {
            bar_msg.dceOptionMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::DCEOptionMultiMinuteBar::MessageID) {
            bar_msg.dceOptionMultiMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::CZCEOptionBar::MessageID) {
            bar_msg.czceOptionBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::DCEOptionBar::MessageID) {
            bar_msg.dceOptionBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::NanHuaFutureMinuteBar::MessageID) {
            bar_msg.nanHuaFutureMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::FXCMMinuteBar_V2::MessageID) {
            bar_msg.fxcmMinuteBar_V2.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::DYFutureIndex::MessageID) {
            bar_msg.dyFutureIndex.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::DYThemeIndex::MessageID) {
            bar_msg.dyThemeIndex.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::SHSZAccuMainMoneyFlow::MessageID) {
            bar_msg.shszAccuMainMoneyFlow.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::SWIndustry::MessageID) {
            bar_msg.swIndustry.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::DeltaTheme::MessageID) {
            bar_msg.deltaTheme.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::CrudeFutureMinuteBar::MessageID) {
            bar_msg.crudeFutureMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::CrudeFutureSecondBar::MessageID) {
            bar_msg.crudeFutureSecondBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::ThemeMinuteBar::MessageID) {
            bar_msg.themeMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::SHSZAccuMoneyFlow::MessageID) {
            bar_msg.shszAccuMoneyFlow.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::SHFEOptionMinuteBar::MessageID) {
            bar_msg.shfeOptionMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::SHFEOptionMultiMinuteBar::MessageID) {
            bar_msg.shfeOptionMultiMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::SHFEOptionBar::MessageID) {
            bar_msg.shfeOptionBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::IndexStatistics::MessageID) {
            bar_msg.indexStatistics.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::SwgIndexMinuteBar::MessageID) {
            bar_msg.swgIndexMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::DYThemeIndexAA::MessageID) {
            bar_msg.dyThemeIndexAA.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::DYThemeMoneyFlow::MessageID) {
            bar_msg.dyThemeMoneyFlow.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::CustomTheme::MessageID) {
            bar_msg.customTheme.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::SHSZDeltaPrice::MessageID) {
            bar_msg.shszDeltaPrice.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::AHComp::MessageID) {
            bar_msg.ahComp.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::SHSZAccuMoneyFlow2::MessageID) {
            bar_msg.shszAccuMoneyFlow2.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::SHSZAccuMoneyFlow2Ext::MessageID) {
            bar_msg.shszAccuMoneyFlow2Ext.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::HKEXIndexMinuteBar::MessageID) {
            bar_msg.hkexIndexMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::MarketMoneyFlow::MessageID) {
            bar_msg.marketMoneyFlow.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::PreMktLimitChange::MessageID) {
            bar_msg.preMktLimitChange.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::SHSZDynamicMinuteBar::MessageID) {
            bar_msg.shszDynamicMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::CFFEXOptionBar::MessageID) {
            bar_msg.cffexOptionBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::XSHEOptionBar::MessageID) {
            bar_msg.xsheOptionBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::CniIndexMinuteBar::MessageID) {
            bar_msg.cniIndexMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::SHSZAccuMoneyFlow2Extra::MessageID) {
            bar_msg.shszAccuMoneyFlow2Extra.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::SHSZMoneyFlow2PriceSpread::MessageID) {
            bar_msg.shszMoneyFlow2PriceSpread.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::DYThemeIndexExtra::MessageID) {
            bar_msg.dyThemeIndexExtra.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::RegionMoneyFlow::MessageID) {
            bar_msg.regionMoneyFlow.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::CsiIndexMinuteBar::MessageID) {
            bar_msg.csiIndexMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::DeltaIndex::MessageID) {
            bar_msg.deltaIndex.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::OrderDetailStat::MessageID) {
            bar_msg.orderDetailStat.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::SHSZLimitStatus::MessageID) {
            bar_msg.shszLimitStatus.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::IndexDynamicBar::MessageID) {
            bar_msg.indexDynamicBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::MarketMetrics::MessageID) {
            bar_msg.marketMetrics.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::DeltaPTFPTick::MessageID) {
            bar_msg.deltaPTFPTick.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::IndexMoneyFlow2::MessageID) {
            bar_msg.indexMoneyFlow2.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::FundsValuation::MessageID) {
            bar_msg.fundsValuation.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::CrudeOptionMinuteBar::MessageID) {
            bar_msg.crudeOptionMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::SWIndustry2::MessageID) {
            bar_msg.swIndustry2.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::XSHEBondMinuteBar::MessageID) {
            bar_msg.xsheBondMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::XSHGBondMinuteBar::MessageID) {
            bar_msg.xshgBondMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::DYThemeIndex2::MessageID) {
            bar_msg.dyThemeIndex2.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::GFEXFutureMinuteBar::MessageID) {
            bar_msg.gfexFutureMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::GFEXOptionMinuteBar::MessageID) {
            bar_msg.gfexOptionMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::ThemeMinuteBar2::MessageID) {
            bar_msg.themeMinuteBar2.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::SHSZDeltaIndex::MessageID) {
            bar_msg.shszDeltaIndex.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::DYThemeIndex3::MessageID) {
            bar_msg.dyThemeIndex3.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::ThemeMinuteBar3::MessageID) {
            bar_msg.themeMinuteBar3.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::BJBondBar::MessageID) {
            bar_msg.bjBondBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::DYThemeMoneyFlow3::MessageID) {
            bar_msg.dyThemeMoneyFlow3.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::TickerChgPct::MessageID) {
            bar_msg.tickerChgPct.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::BJStockMinuteBar::MessageID) {
            bar_msg.bjStockMinuteBar.emplace_back(msg->Copy());
        } else if (head->MessageID == mdl_bar_msg::ETFMoneyFlow::MessageID) {
            bar_msg.etfMoneyFlow.emplace_back(msg->Copy());
        }
        m_OnMessageCallback(SubID(MDLSID_MDL_BAR, head->MessageID), msg);
    }

    void SetCallback(CallbackFunc &&callback) {
        m_OnMessageCallback = callback;
    }

private:
    IOManagerPtr m_IOManager;
    CallbackFunc m_OnMessageCallback;
};
