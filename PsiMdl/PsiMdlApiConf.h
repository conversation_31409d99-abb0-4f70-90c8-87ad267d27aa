#pragma once
#include<string>
#include<vector>
struct MdlApiConf {
    std::string ServerAddr;
    std::string ServerPort;
    std::string Token;

    std::vector<int>SHL1;
    std::vector<int>SHL2;
    std::vector<int>SZL1;
    std::vector<int>SZL2;
    std::vector<int>DCE;
    std::vector<int>DCEL2;
    std::vector<int>SHFE;
    std::vector<int>SHFEL2;
    std::vector<int>CZCE;
    std::vector<int>CZCEL2;
    std::vector<int>CFFEX;
    std::vector<int>CFFEXL2;
    std::vector<int>HKEX;
    std::vector<int>GFEX;
    std::vector<int>GFEXL2;
    std::vector<int>SWG;
    std::vector<int>NEEQ;
    std::vector<int>BAR;
    std::vector<int>SHNY;
    std::vector<int>DigiCur;
    std::vector<int>CSI;
    std::vector<int>CNI;
};
inline MdlApiConf mdl_conf;
inline void parseMdlApiConf(const YAML::Node& node, MdlApiConf& conf)
{
    try {
        conf.ServerAddr = node["ServerAddr"].as<std::string>();
        conf.ServerPort = node["ServerPort"].as<std::string>();
        conf.Token = node["Token"].as<std::string>();
        auto &subscribe_node=node["Subscribe"];
        for (const auto& item : subscribe_node["SHL1"]) {
            conf.SHL1.push_back(item.second.as<int>());
        }
        for (const auto& item : subscribe_node["SHL2"]) {
            conf.SHL2.push_back(item.second.as<int>());
        }
        for (const auto& item : subscribe_node["SZL1"]) {
            conf.SZL1.push_back(item.second.as<int>());
        }
        for (const auto& item : subscribe_node["SZL2"]) {
            conf.SZL2.push_back(item.second.as<int>());
        }
        for (const auto& item : subscribe_node["DCE"]) {
            conf.DCE.push_back(item.second.as<int>());
        }
        for (const auto& item : subscribe_node["DCEL2"]) {
            conf.DCEL2.push_back(item.second.as<int>());
        }
        for (const auto& item : subscribe_node["SHFE"]) {
            conf.SHFE.push_back(item.second.as<int>());
        }
        for (const auto& item : subscribe_node["SHFEL2"]) {
            conf.SHFEL2.push_back(item.second.as<int>());
        }
        for (const auto& item : subscribe_node["CZCE"]) {
            conf.CZCE.push_back(item.second.as<int>());
        }
        for (const auto& item : subscribe_node["CZCEL2"]) {
            conf.CZCEL2.push_back(item.second.as<int>());
        }
        for (const auto& item : subscribe_node["CFFEX"]) {
            conf.CFFEX.push_back(item.second.as<int>());
        }
        for (const auto& item : subscribe_node["CFFEXL2"]) {
            conf.CFFEXL2.push_back(item.second.as<int>());
        }
        for (const auto& item : subscribe_node["HKEX"]) {
            conf.HKEX.push_back(item.second.as<int>());
        }
        for (const auto& item : subscribe_node["GFEX"]) {
            conf.GFEX.push_back(item.second.as<int>());
        }
        for (const auto& item : subscribe_node["GFEXL2"]) {
            conf.GFEXL2.push_back(item.second.as<int>());
        }
        for (const auto& item : subscribe_node["SWG"]) {
            conf.SWG.push_back(item.second.as<int>());
        }
        for (const auto& item : subscribe_node["NEEQ"]) {
            conf.NEEQ.push_back(item.second.as<int>());
        }
        for (const auto& item : subscribe_node["BAR"]) {
            conf.BAR.push_back(item.second.as<int>());
        }
        for (const auto& item : subscribe_node["SHNY"]) {
            conf.SHNY.push_back(item.second.as<int>());
        }
        for (const auto& item : subscribe_node["DigiCur"]) {
            conf.DigiCur.push_back(item.second.as<int>());
        }
        for (const auto& item : subscribe_node["CSI"]) {
            conf.CSI.push_back(item.second.as<int>());
        }
        for (const auto& item : subscribe_node["CNI"]) {
            conf.CNI.push_back(item.second.as<int>());
        }

    }
    catch (const YAML::Exception& e)
    {
        std::cerr << "YAML error: " << e.what() << std::endl;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Error: " << e.what() << std::endl;
    }
}