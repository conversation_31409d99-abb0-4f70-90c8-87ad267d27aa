// Generated by the code_gen tool.  DO NOT EDIT!
#pragma once

#include "mdl_api_types.h"

namespace datayes {
namespace mdl {
namespace mdl_cffexl2_msg {

static const uint16_t MDLVID_MDL_CFFEXL2 = 101;

enum MDL_CFFEXL2MessageID {
	MDLMID_MDL_CFFEXL2_Future = 1,
	MDLMID_MDL_CFFEXL2_Option = 2
};

#pragma pack(1)

struct Future {
	enum {
		ServiceID = MDLSID_MDL_CFFEXL2,
		ServiceVer = MDLVID_MDL_CFFEXL2,
		MessageID = MDLMID_MDL_CFFEXL2_Future
	};
	MDLDate ActionDay;
	MDLDate TradDay;
	MDLTime UpdateTime;
	MDLAnsiString InstruID;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> OpenPrice;
	int32_t Volume;
	MDLDoubleT<3> Turnover;
	MDLDoubleT<3> OpenInt;
	MDLDoubleT<3> PreOpenInt;
	MDLDoubleT<3> AveragePrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> SetPrice;
	MDLDoubleT<3> PreCloPrice;
	MDLDoubleT<3> PreSetPrice;
	MDLDoubleT<3> CurrDelta;
	MDLDoubleT<3> PreDelta;
	struct BidPriceLevelItem {
		int32_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct AskPriceLevelItem {
		int32_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
	MDLDoubleT<3> ULimitPrice;
	MDLDoubleT<3> LLimitPrice;
};

struct Option {
	enum {
		ServiceID = MDLSID_MDL_CFFEXL2,
		ServiceVer = MDLVID_MDL_CFFEXL2,
		MessageID = MDLMID_MDL_CFFEXL2_Option
	};
	MDLDate ActionDay;
	MDLDate TradDay;
	MDLTime UpdateTime;
	MDLAnsiString InstruID;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> OpenPrice;
	int32_t Volume;
	MDLDoubleT<3> Turnover;
	MDLDoubleT<3> OpenInt;
	MDLDoubleT<3> PreOpenInt;
	MDLDoubleT<3> AveragePrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> SetPrice;
	MDLDoubleT<3> PreCloPrice;
	MDLDoubleT<3> PreSetPrice;
	MDLDoubleT<3> CurrDelta;
	MDLDoubleT<3> PreDelta;
	struct BidPriceLevelItem {
		int32_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct AskPriceLevelItem {
		int32_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
	MDLDoubleT<3> ULimitPrice;
	MDLDoubleT<3> LLimitPrice;
};

#pragma pack()

} // namespace mdl_cffexl2_msg
} // namespace mdl
} // namespace datayes
