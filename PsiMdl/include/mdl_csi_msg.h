// Generated by the code_gen tool.  DO NOT EDIT!
#pragma once

#include "mdl_api_types.h"

namespace datayes {
namespace mdl {
namespace mdl_csi_msg {

static const uint16_t MDLVID_MDL_CSI = 101;

enum MDL_CSIMessageID {
	MDLMID_MDL_CSI_CSIndex = 1,
	MDLMID_MDL_CSI_EtfIopv = 2
};

#pragma pack(1)

struct CSIndex {
	enum {
		ServiceID = MDLSID_MDL_CSI,
		ServiceVer = MDLVID_MDL_CSI,
		MessageID = MDLMID_MDL_CSI_CSIndex
	};
	MDLAnsiString IndexCode;
	MDLUTF8String IndexName;
	MDLDate TradeDate;
	MDLDate LocalDate;
	MDLTime DataTime;
	uint32_t MarketCode;
	MDLDoubleT<4> LastPrice;
	MDLDoubleT<4> HighPrice;
	MDLDoubleT<4> LowPrice;
	MDLDoubleT<4> OpenPrice;
	MDLDoubleT<4> PreClosePrice;
	MDLDoubleT<4> ClosePrice;
	MDLDoubleT<4> Change;
	MDLDoubleT<4> ChgPct;
	int64_t Volume;
	MDLDoubleT<5> Turnover;
	MDLDoubleT<8> ExchangeRate;
	uint32_t CurrencyType;
	MDLDoubleT<4> ClosePrice2;
	MDLDoubleT<4> ClosePrice3;
};

struct EtfIopv {
	enum {
		ServiceID = MDLSID_MDL_CSI,
		ServiceVer = MDLVID_MDL_CSI,
		MessageID = MDLMID_MDL_CSI_EtfIopv
	};
	MDLAnsiString SecurityCode;
	MDLUTF8String SecurityName;
	uint32_t MarketCode;
	MDLDate DataDate;
	MDLTime DataTime;
	MDLDoubleT<4> IOPV;
};

#pragma pack()

} // namespace mdl_csi_msg
} // namespace mdl
} // namespace datayes
