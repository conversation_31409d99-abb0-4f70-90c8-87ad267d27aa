// Generated by the code_gen tool.  DO NOT EDIT!
#pragma once

#include "mdl_api_types.h"

namespace datayes {
namespace mdl {
namespace mdl_dcel2_msg {

static const uint16_t MDLVID_MDL_DCEL2 = 101;

enum MDL_DCEL2MessageID {
	MDLMID_MDL_DCEL2_Future = 1,
	MDLMID_MDL_DCEL2_Option = 2,
	MDLMID_MDL_DCEL2_FutureTrdQty = 3,
	MDLMID_MDL_DCEL2_OptionTrdQty = 4,
	MDLMID_MDL_DCEL2_CmbTypeInfo = 5,
	MDLMID_MDL_DCEL2_OptionParam = 6,
	MDLMID_MDL_DCEL2_FutureOrder = 7,
	MDLMID_MDL_DCEL2_OptionOrder = 8,
	MDLMID_MDL_DCEL2_CmbOrder = 9,
	MDLMID_MDL_DCEL2_OrderStat = 10
};

#pragma pack(1)

struct Future {
	enum {
		ServiceID = MDLSID_MDL_DCEL2,
		ServiceVer = MDLVID_MDL_DCEL2,
		MessageID = MDLMID_MDL_DCEL2_Future
	};
	MDLDate ActionDay;
	MDLDate TradDay;
	MDLTime UpdateTime;
	MDLAnsiString InstruID;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> OpenPrice;
	int64_t LastVolume;
	int64_t Volume;
	MDLDoubleT<3> Turnover;
	int64_t OpenInt;
	int64_t PreOpenInt;
	int64_t OpenIntChg;
	MDLDoubleT<3> AveragePrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> SetPrice;
	MDLDoubleT<3> PreSetPrice;
	MDLDoubleT<3> PreCloPrice;
	int64_t BuyVolume;
	int64_t SellVolume;
	MDLDoubleT<3> AvgBuyPrice;
	MDLDoubleT<3> AvgSellPrice;
	struct BidBookItem {
		MDLDoubleT<3> Price;
		int64_t Volume;
		int32_t DerVolume;
	};
	MDLListT<BidBookItem> BidBook;
	struct AskBookItem {
		MDLDoubleT<3> Price;
		int64_t Volume;
		int32_t DerVolume;
	};
	MDLListT<AskBookItem> AskBook;
	MDLDoubleT<3> ULimitPrice;
	MDLDoubleT<3> LLimitPrice;
	MDLDoubleT<3> LifeHighPrice;
	MDLDoubleT<3> LifeLowPrice;
};

struct Option {
	enum {
		ServiceID = MDLSID_MDL_DCEL2,
		ServiceVer = MDLVID_MDL_DCEL2,
		MessageID = MDLMID_MDL_DCEL2_Option
	};
	MDLDate ActionDay;
	MDLDate TradDay;
	MDLTime UpdateTime;
	MDLAnsiString InstruID;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> OpenPrice;
	int64_t LastVolume;
	int64_t Volume;
	MDLDoubleT<3> Turnover;
	int64_t OpenInt;
	int64_t PreOpenInt;
	int64_t OpenIntChg;
	MDLDoubleT<3> AveragePrice;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> SetPrice;
	MDLDoubleT<3> PreSetPrice;
	MDLDoubleT<3> PreCloPrice;
	int64_t BuyVolume;
	int64_t SellVolume;
	MDLDoubleT<3> AvgBuyPrice;
	MDLDoubleT<3> AvgSellPrice;
	struct BidBookItem {
		MDLDoubleT<3> Price;
		int64_t Volume;
		int32_t DerVolume;
	};
	MDLListT<BidBookItem> BidBook;
	struct AskBookItem {
		MDLDoubleT<3> Price;
		int64_t Volume;
		int32_t DerVolume;
	};
	MDLListT<AskBookItem> AskBook;
	MDLDoubleT<3> ULimitPrice;
	MDLDoubleT<3> LLimitPrice;
	MDLDoubleT<3> LifeHighPrice;
	MDLDoubleT<3> LifeLowPrice;
};

struct FutureTrdQty {
	enum {
		ServiceID = MDLSID_MDL_DCEL2,
		ServiceVer = MDLVID_MDL_DCEL2,
		MessageID = MDLMID_MDL_DCEL2_FutureTrdQty
	};
	MDLDate ActionDay;
	MDLDate TradDay;
	MDLTime UpdateTime;
	MDLAnsiString InstruID;
	struct StatusFieldItem {
		MDLDoubleT<3> Price;
		int64_t BuyOpenVol;
		int64_t BuyClsVol;
		int64_t SellOpenVol;
		int64_t SellClsVol;
	};
	MDLListT<StatusFieldItem> StatusField;
};

struct OptionTrdQty {
	enum {
		ServiceID = MDLSID_MDL_DCEL2,
		ServiceVer = MDLVID_MDL_DCEL2,
		MessageID = MDLMID_MDL_DCEL2_OptionTrdQty
	};
	MDLDate ActionDay;
	MDLDate TradDay;
	MDLTime UpdateTime;
	MDLAnsiString InstruID;
	struct StatusFieldItem {
		MDLDoubleT<3> Price;
		int64_t BuyOpenVol;
		int64_t BuyClsVol;
		int64_t SellOpenVol;
		int64_t SellClsVol;
	};
	MDLListT<StatusFieldItem> StatusField;
};

struct CmbTypeInfo {
	enum {
		ServiceID = MDLSID_MDL_DCEL2,
		ServiceVer = MDLVID_MDL_DCEL2,
		MessageID = MDLMID_MDL_DCEL2_CmbTypeInfo
	};
	MDLDate ActionDay;
	MDLDate TradDay;
	MDLTime UpdateTime;
	MDLAnsiString CmbtypeId;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> LastTradeVolume;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LifeLowPrice;
	MDLDoubleT<3> LifeHighPrice;
	MDLDoubleT<3> ULimitPrice;
	MDLDoubleT<3> LLimitPrice;
	struct BidBookItem {
		MDLDoubleT<3> Price;
		int64_t Volume;
	};
	MDLListT<BidBookItem> BidBook;
	struct AskBookItem {
		MDLDoubleT<3> Price;
		int64_t Volume;
	};
	MDLListT<AskBookItem> AskBook;
};

struct OptionParam {
	enum {
		ServiceID = MDLSID_MDL_DCEL2,
		ServiceVer = MDLVID_MDL_DCEL2,
		MessageID = MDLMID_MDL_DCEL2_OptionParam
	};
	MDLDate ActionDay;
	MDLDate TradDay;
	MDLTime UpdateTime;
	MDLAnsiString InstruID;
	MDLDoubleT<4> Delta;
	MDLDoubleT<4> Gama;
	MDLDoubleT<4> Rho;
	MDLDoubleT<4> Theta;
	MDLDoubleT<4> Vega;
};

struct FutureOrder {
	enum {
		ServiceID = MDLSID_MDL_DCEL2,
		ServiceVer = MDLVID_MDL_DCEL2,
		MessageID = MDLMID_MDL_DCEL2_FutureOrder
	};
	MDLDate ActionDay;
	MDLDate TradDay;
	MDLTime UpdateTime;
	MDLAnsiString InstruID;
	MDLDoubleT<3> BidPrice;
	struct BidOrdersItem {
		int64_t OrderQty;
	};
	MDLListT<BidOrdersItem> BidOrders;
	MDLDoubleT<3> AskPrice;
	struct AskOrdersItem {
		int64_t OrderQty;
	};
	MDLListT<AskOrdersItem> AskOrders;
};

struct OptionOrder {
	enum {
		ServiceID = MDLSID_MDL_DCEL2,
		ServiceVer = MDLVID_MDL_DCEL2,
		MessageID = MDLMID_MDL_DCEL2_OptionOrder
	};
	MDLDate ActionDay;
	MDLDate TradDay;
	MDLTime UpdateTime;
	MDLAnsiString InstruID;
	MDLDoubleT<3> BidPrice;
	struct BidOrdersItem {
		int64_t OrderQty;
	};
	MDLListT<BidOrdersItem> BidOrders;
	MDLDoubleT<3> AskPrice;
	struct AskOrdersItem {
		int64_t OrderQty;
	};
	MDLListT<AskOrdersItem> AskOrders;
};

struct CmbOrder {
	enum {
		ServiceID = MDLSID_MDL_DCEL2,
		ServiceVer = MDLVID_MDL_DCEL2,
		MessageID = MDLMID_MDL_DCEL2_CmbOrder
	};
	MDLDate ActionDay;
	MDLDate TradDay;
	MDLTime UpdateTime;
	MDLAnsiString InstruID;
	MDLDoubleT<3> BidPrice;
	struct BidOrdersItem {
		int64_t OrderQty;
	};
	MDLListT<BidOrdersItem> BidOrders;
	MDLDoubleT<3> AskPrice;
	struct AskOrdersItem {
		int64_t OrderQty;
	};
	MDLListT<AskOrdersItem> AskOrders;
};

struct OrderStat {
	enum {
		ServiceID = MDLSID_MDL_DCEL2,
		ServiceVer = MDLVID_MDL_DCEL2,
		MessageID = MDLMID_MDL_DCEL2_OrderStat
	};
	MDLDate ActionDay;
	MDLDate TradDay;
	MDLTime UpdateTime;
	MDLAnsiString InstruID;
	int64_t BuyVolume;
	int64_t SellVolume;
	MDLDoubleT<3> AvgBuyPrice;
	MDLDoubleT<3> AvgSellPrice;
};

#pragma pack()

} // namespace mdl_dcel2_msg
} // namespace mdl
} // namespace datayes
