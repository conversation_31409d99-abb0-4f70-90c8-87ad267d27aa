// Generated by the code_gen tool.  DO NOT EDIT!
#pragma once

#include "mdl_api_types.h"

namespace datayes {
namespace mdl {
namespace mdl_shfel2_msg {

static const uint16_t MDLVID_MDL_SHFEL2 = 101;

enum MDL_SHFEL2MessageID {
	MDLMID_MDL_SHFEL2_CTPFuture = 1,
	MDLMID_MDL_SHFEL2_CTPOption = 2,
	MDLMID_MDL_SHFEL2_CrudeFuture = 3,
	MDLMID_MDL_SHFEL2_CrudeOption = 4
};

#pragma pack(1)

struct CTPFuture {
	enum {
		ServiceID = MDLSID_MDL_SHFEL2,
		ServiceVer = MDLVID_MDL_SHFEL2,
		MessageID = MDLMID_MDL_SHFEL2_CTPFuture
	};
	MDLAnsiString InstruID;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> PreSetPrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> Turnover;
	MDLDoubleT<3> OpenInt;
	MDLDoubleT<3> SetPrice;
	MDLDoubleT<3> ULimitPrice;
	MDLDoubleT<3> LLimitPrice;
	MDLDate TradDay;
	MDLDoubleT<3> PreCloPrice;
	int32_t Volume;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> PreDelta;
	MDLDoubleT<3> CurrDelta;
	MDLTime UpdateTime;
	MDLDoubleT<3> PreOpenInt;
	MDLDoubleT<3> AveragePrice;
	MDLDate ActionDay;
	struct BidBookItem {
		int32_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<BidBookItem> BidBook;
	struct AskBookItem {
		int32_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<AskBookItem> AskBook;
	struct ExtraFieldsItem {
		int64_t Value;
	};
	MDLListT<ExtraFieldsItem> ExtraFields;
};

struct CTPOption {
	enum {
		ServiceID = MDLSID_MDL_SHFEL2,
		ServiceVer = MDLVID_MDL_SHFEL2,
		MessageID = MDLMID_MDL_SHFEL2_CTPOption
	};
	MDLAnsiString InstruID;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> PreSetPrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> Turnover;
	MDLDoubleT<3> OpenInt;
	MDLDoubleT<3> SetPrice;
	MDLDoubleT<3> ULimitPrice;
	MDLDoubleT<3> LLimitPrice;
	MDLDate TradDay;
	MDLDoubleT<3> PreCloPrice;
	int32_t Volume;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> PreDelta;
	MDLDoubleT<3> CurrDelta;
	MDLTime UpdateTime;
	MDLDoubleT<3> PreOpenInt;
	MDLDoubleT<3> AveragePrice;
	MDLDate ActionDay;
	struct BidBookItem {
		int32_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<BidBookItem> BidBook;
	struct AskBookItem {
		int32_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<AskBookItem> AskBook;
	struct ExtraFieldsItem {
		int64_t Value;
	};
	MDLListT<ExtraFieldsItem> ExtraFields;
};

struct CrudeFuture {
	enum {
		ServiceID = MDLSID_MDL_SHFEL2,
		ServiceVer = MDLVID_MDL_SHFEL2,
		MessageID = MDLMID_MDL_SHFEL2_CrudeFuture
	};
	MDLAnsiString InstruID;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> PreSetPrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> Turnover;
	MDLDoubleT<3> OpenInt;
	MDLDoubleT<3> SetPrice;
	MDLDoubleT<3> ULimitPrice;
	MDLDoubleT<3> LLimitPrice;
	MDLDate TradDay;
	MDLDoubleT<3> PreCloPrice;
	int32_t Volume;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> PreDelta;
	MDLDoubleT<3> CurrDelta;
	MDLTime UpdateTime;
	MDLDoubleT<3> PreOpenInt;
	MDLDoubleT<3> AveragePrice;
	MDLDate ActionDay;
	struct BidBookItem {
		int32_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<BidBookItem> BidBook;
	struct AskBookItem {
		int32_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<AskBookItem> AskBook;
	struct ExtraFieldsItem {
		int64_t Value;
	};
	MDLListT<ExtraFieldsItem> ExtraFields;
};

struct CrudeOption {
	enum {
		ServiceID = MDLSID_MDL_SHFEL2,
		ServiceVer = MDLVID_MDL_SHFEL2,
		MessageID = MDLMID_MDL_SHFEL2_CrudeOption
	};
	MDLAnsiString InstruID;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> PreSetPrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> Turnover;
	MDLDoubleT<3> OpenInt;
	MDLDoubleT<3> SetPrice;
	MDLDoubleT<3> ULimitPrice;
	MDLDoubleT<3> LLimitPrice;
	MDLDate TradDay;
	MDLDoubleT<3> PreCloPrice;
	int32_t Volume;
	MDLDoubleT<3> ClosePrice;
	MDLDoubleT<3> PreDelta;
	MDLDoubleT<3> CurrDelta;
	MDLTime UpdateTime;
	MDLDoubleT<3> PreOpenInt;
	MDLDoubleT<3> AveragePrice;
	MDLDate ActionDay;
	struct BidBookItem {
		int32_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<BidBookItem> BidBook;
	struct AskBookItem {
		int32_t Volume;
		MDLDoubleT<3> Price;
	};
	MDLListT<AskBookItem> AskBook;
	struct ExtraFieldsItem {
		int64_t Value;
	};
	MDLListT<ExtraFieldsItem> ExtraFields;
};

#pragma pack()

} // namespace mdl_shfel2_msg
} // namespace mdl
} // namespace datayes
