// Generated by the code_gen tool.  DO NOT EDIT!
#pragma once

#include "mdl_api_types.h"

namespace datayes {
namespace mdl {
namespace mdl_shl2_msg {

static const uint16_t MDLVID_MDL_SHL2 = 101;

enum MDL_SHL2MessageID {
	MDLMID_MDL_SHL2_SHL1Stock = 2,
	MDLMID_MDL_SHL2_SHL2Transaction = 3,
	MDLMID_MDL_SHL2_SHL2MarketData = 4,
	MDLMID_MDL_SHL2_SHL2VirtualAuctionPrice = 5,
	MDLMID_MDL_SHL2_SHL2Index = 6,
	MDLMID_MDL_SHL2_SHL2MarketOverview = 7,
	MDLMID_MDL_SHL2_SHL2Statics = 8,
	MDLMID_MDL_SHL2_OPTLevel1 = 9,
	MDLMID_MDL_SHL2_MDSnapshotFullRefresh_4001 = 10,
	MDLMID_MDL_SHL2_FixedIncomePrice = 11,
	MDLMID_MDL_SHL2_FixedIncomeInfo = 12,
	MDLMID_MDL_SHL2_FixedIncomeTransaction = 13,
	MDLMID_MDL_SHL2_FixedIncomeFirmQuote = 14,
	MDLMID_MDL_SHL2_RealQuota = 15,
	MDLMID_MDL_SHL2_ATPMarketData = 16,
	MDLMID_MDL_SHL2_ATPTransaction = 17,
	MDLMID_MDL_SHL2_SHL2Transaction2 = 18,
	MDLMID_MDL_SHL2_Order = 19,
	MDLMID_MDL_SHL2_BondMarketData = 20,
	MDLMID_MDL_SHL2_BondTick = 21,
	MDLMID_MDL_SHL2_BondOrderQueue = 22,
	MDLMID_MDL_SHL2_MarketInfo = 23,
	MDLMID_MDL_SHL2_NGTSTick = 24,
	MDLMID_MDL_SHL2_ETFMarketData = 25,
	MDLMID_MDL_SHL2_BondDist = 26,
	MDLMID_MDL_SHL2_BondDistOrderQueue = 27,
	MDLMID_MDL_SHL2_BondDistTick = 28
};

#pragma pack(1)

struct SHL1Stock {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_SHL1Stock
	};
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	MDLFloatT<3> PreCloPrice;
	MDLFloatT<3> OpenPrice;
	int64_t Turnover;
	MDLFloatT<3> HighPrice;
	MDLFloatT<3> LowPrice;
	MDLFloatT<3> LastPrice;
	uint64_t Volume;
	MDLFloatT<3> PE;
	uint64_t BidVolume1;
	MDLFloatT<3> BidPrice1;
	uint64_t BidVolume2;
	MDLFloatT<3> BidPrice2;
	uint64_t BidVolume3;
	MDLFloatT<3> BidPrice3;
	uint64_t BidVolume4;
	MDLFloatT<3> BidPrice4;
	uint64_t BidVolume5;
	MDLFloatT<3> BidPrice5;
	uint64_t AskVolume1;
	MDLFloatT<3> AskPrice1;
	uint64_t AskVolume2;
	MDLFloatT<3> AskPrice2;
	uint64_t AskVolume3;
	MDLFloatT<3> AskPrice3;
	uint64_t AskVolume4;
	MDLFloatT<3> AskPrice4;
	uint64_t AskVolume5;
	MDLFloatT<3> AskPrice5;
};

struct SHL2Transaction {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_SHL2Transaction
	};
	int32_t DataStatus;
	int32_t TradeIndex;
	int32_t TradeChan;
	MDLAnsiString SecurityID;
	MDLTime TradTime;
	MDLFloatT<3> TradPrice;
	MDLDoubleT<3> TradVolume;
	MDLDoubleT<5> TradeMoney;
	int64_t TradeBuyNo;
	int64_t TradeSellNo;
	MDLAnsiString TradeBSFlag;
};

struct SHL2MarketData {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_SHL2MarketData
	};
	MDLTime UpdateTime;
	MDLAnsiString SecurityID;
	int32_t ImageStatus;
	MDLFloatT<3> PreCloPrice;
	MDLFloatT<3> OpenPrice;
	MDLFloatT<3> HighPrice;
	MDLFloatT<3> LowPrice;
	MDLFloatT<3> LastPrice;
	MDLFloatT<3> ClosePrice;
	MDLAnsiString InstruStatus;
	uint32_t TradNumber;
	MDLDoubleT<3> TradVolume;
	MDLDoubleT<5> Turnover;
	MDLDoubleT<3> TotalBidVol;
	MDLFloatT<3> WAvgBidPri;
	MDLFloatT<3> AltWAvgBidPri;
	MDLDoubleT<3> TotalAskVol;
	MDLFloatT<3> WAvgAskPri;
	MDLFloatT<3> AltWAvgAskPri;
	uint32_t EtfBuyNumber;
	MDLDoubleT<3> EtfBuyVolume;
	MDLDoubleT<5> EtfBuyMoney;
	uint32_t EtfSellNumber;
	MDLDoubleT<3> EtfSellVolume;
	MDLDoubleT<5> ETFSellMoney;
	MDLFloatT<4> YieldToMatu;
	MDLDoubleT<3> TotWarExNum;
	MDLDoubleT<3> WarLowerPri;
	MDLDoubleT<5> WarUpperPri;
	uint32_t WiDBuyNum;
	MDLDoubleT<3> WiDBuyVol;
	MDLDoubleT<5> WiDBuyMon;
	uint32_t WiDSellNum;
	MDLDoubleT<3> WiDSellVol;
	MDLDoubleT<5> WiDSellMon;
	uint32_t TotBidNum;
	uint32_t TotSellNum;
	uint32_t MaxBidDur;
	uint32_t MaxSellDur;
	uint32_t BidNum;
	uint32_t SellNum;
	struct BidLevelsItem {
		uint32_t PriLevOpera;
		MDLFloatT<3> OrderPrice;
		MDLDoubleT<3> OrderVol;
		uint32_t OrderNum;
		struct NOrdersItem {
			uint32_t OrderQueOper;
			uint32_t OrderQueID;
			MDLDoubleT<3> OrderQty;
		};
		MDLListT<NOrdersItem> NOrders;
	};
	MDLListT<BidLevelsItem> BidLevels;
	struct SellLevelsItem {
		uint32_t PriLevOpera;
		MDLFloatT<3> OrderPrice;
		MDLDoubleT<3> OrderVol;
		uint32_t OrderNum;
		struct NoOrdersItem {
			uint32_t OrderQueOper;
			uint32_t OrderQueID;
			MDLDoubleT<3> OrderQty;
		};
		MDLListT<NoOrdersItem> NoOrders;
	};
	MDLListT<SellLevelsItem> SellLevels;
	MDLFloatT<3> IOPV;
};

struct SHL2VirtualAuctionPrice {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_SHL2VirtualAuctionPrice
	};
	MDLTime UpdateTime;
	uint32_t DataStatus;
	MDLAnsiString SecurityID;
	MDLFloatT<3> VirPri;
	MDLDoubleT<3> VirAucQty;
	MDLDoubleT<3> LeaQty;
	MDLAnsiString Side;
};

struct SHL2Index {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_SHL2Index
	};
	MDLTime UpdateTime;
	uint32_t DataStatus;
	MDLAnsiString SecurityID;
	MDLDoubleT<5> PreCloseIndex;
	MDLDoubleT<5> OpenIndex;
	MDLDoubleT<1> Turnover;
	MDLDoubleT<5> HighIndex;
	MDLDoubleT<5> LowIndex;
	MDLDoubleT<5> LastIndex;
	MDLTime TradTime;
	MDLDoubleT<5> TradVolume;
	MDLDoubleT<5> CloseIndex;
};

struct SHL2MarketOverview {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_SHL2MarketOverview
	};
	MDLTime UpdateTime;
	uint32_t DataStatus;
	MDLAnsiString SecurityID;
	uint32_t UpdateMill;
	MDLDate TradDay;
};

struct SHL2Statics {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_SHL2Statics
	};
	MDLAnsiString SecurityID;
	MDLTime UpdateTime;
	MDLFloatT<3> OpenPrice;
	MDLFloatT<3> HighPrice;
	MDLFloatT<3> LowPrice;
	MDLFloatT<3> ClosePrice;
	MDLDoubleT<3> TradVolume;
	MDLDoubleT<5> Turnover;
};

struct OPTLevel1 {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_OPTLevel1
	};
	MDLTime UpdateTime;
	int32_t DataStatus;
	MDLAnsiString SecurityID;
	int32_t ImageStatus;
	MDLDoubleT<4> PreSettlPrice;
	MDLDoubleT<4> SettlPrice;
	MDLDoubleT<4> OpenPx;
	MDLDoubleT<4> HighPx;
	MDLDoubleT<4> LowPx;
	MDLDoubleT<4> LastPx;
	MDLDoubleT<4> ClosePx;
	MDLDoubleT<4> AuctionPrice;
	int64_t AuctionQty;
	int64_t TotalLongPosition;
	struct BidPriceLevelItem {
		int64_t BidSize;
		MDLDoubleT<4> BidPx;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
	struct OfferPriceLevelItem {
		int64_t OfferSize;
		MDLDoubleT<4> OfferPx;
	};
	MDLListT<OfferPriceLevelItem> OfferPriceLevel;
	int64_t TotalVolumeTrade;
	MDLDoubleT<2> TotalValueTrade;
	MDLAnsiString TradingPhaseCode;
	MDLTime OrigTime;
};

struct MDSnapshotFullRefresh_4001 {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_MDSnapshotFullRefresh_4001
	};
	MDLAnsiString MDStreamID;
	MDLAnsiString SecurityID;
	MDLUTF8String Symbol;
	int64_t NumTrades;
	int64_t TradeVolume;
	MDLDoubleT<2> TotalValueTraded;
	MDLDoubleT<4> PrevClosePx;
	MDLDoubleT<4> PrevSetPx;
	int64_t TotalLongPosition;
	struct MDFullGrpItem {
		MDLDoubleT<4> MDEntryPx;
		int64_t MDEntrySize;
		MDLTime MDEntryTime;
		uint32_t MDEntryPositionNo;
		MDLAnsiString MDEntryType;
	};
	MDLListT<MDFullGrpItem> MDFullGrp;
	MDLAnsiString TradingPhaseCode;
};

struct FixedIncomePrice {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_FixedIncomePrice
	};
	MDLDate UpdateDate;
	MDLTime UpdateTime;
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	MDLTime TradeTime;
	MDLDoubleT<3> PreClosePrice;
	MDLDoubleT<3> PreWPrice;
	MDLDoubleT<3> OpenPrice;
	MDLDoubleT<3> HighPrice;
	MDLDoubleT<3> LowPrice;
	MDLDoubleT<3> LastPrice;
	MDLDoubleT<3> WPrice;
	int64_t Volume;
	MDLDoubleT<1> Value;
	int64_t Deal;
	MDLDoubleT<4> PreCloseRate;
	MDLDoubleT<4> PreWRate;
	MDLDoubleT<4> OpenRate;
	MDLDoubleT<4> HighRate;
	MDLDoubleT<4> LowRate;
	MDLDoubleT<4> LastRate;
	MDLDoubleT<4> WRate;
};

struct FixedIncomeInfo {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_FixedIncomeInfo
	};
	MDLDate UpdateDate;
	MDLTime UpdateTime;
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	uint32_t BondType;
	MDLAnsiString BondProperty;
	MDLAnsiString BondStatusCD;
	MDLAnsiString PleTickerSymbol;
	MDLTime OpenTime;
	MDLTime CloseTime;
	MDLAnsiString IssueModeCD;
	MDLDoubleT<3> PAR;
	MDLDoubleT<3> IssuePrice;
	MDLAnsiString IntType;
	MDLAnsiString CPNFreqCD;
	MDLDoubleT<3> Coupon;
	MDLDoubleT<3> PrimeRate;
	MDLDoubleT<3> BasicSpread;
	uint32_t Maturity;
	int64_t ActualTenderSize;
	MDLDate IssueBeginDate;
	MDLDate IssueEndDate;
	MDLDate ListDate;
	MDLDate MaturityDate;
	MDLAnsiString TreasuryType;
	MDLAnsiString UnderWritingMethod;
	MDLAnsiString IsCrossMkt;
	MDLAnsiString IsShort;
	int64_t ShortSize;
	int64_t BrokerShortSize;
	MDLDoubleT<3> PreClosePrice;
	MDLDoubleT<3> PreWPrice;
};

struct FixedIncomeTransaction {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_FixedIncomeTransaction
	};
	MDLDate UpdateDate;
	MDLTime UpdateTime;
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	MDLDate TradeDate;
	MDLTime TradeTime;
	MDLDoubleT<3> NetPrice;
	MDLDoubleT<4> AI;
	MDLDoubleT<3> GrossPrice;
	MDLDoubleT<4> YTM;
	int64_t Volume;
	MDLDoubleT<1> Value;
	uint32_t Type;
};

struct FixedIncomeFirmQuote {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_FixedIncomeFirmQuote
	};
	MDLDate UpdateDate;
	MDLTime UpdateTime;
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	MDLAnsiString BidNo;
	MDLTime BidTime;
	MDLUTF8String BidBroker;
	MDLDoubleT<3> NetBidPrice;
	int64_t BidVolume;
	MDLDoubleT<3> GrossBidPrice;
	MDLDoubleT<4> BidYTM;
	MDLAnsiString AskNo;
	MDLTime AskTime;
	MDLUTF8String AskBroker;
	MDLDoubleT<3> NetAskPrice;
	int64_t AskVolume;
	MDLDoubleT<3> GrossAskPrice;
	MDLDoubleT<4> AskYTM;
	MDLDoubleT<4> AI;
};

struct RealQuota {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_RealQuota
	};
	MDLTime DataTime;
	MDLAnsiString Market;
	MDLAnsiString Direction;
	int64_t InitQuota;
	int64_t CurrentQuota;
	int64_t UsedQuota;
	MDLAnsiString Status;
};

struct ATPMarketData {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_ATPMarketData
	};
	MDLTime UpdateTime;
	MDLAnsiString SecurityID;
	int32_t ImageStatus;
	MDLFloatT<3> ClosePx;
	MDLAnsiString InstrumentStatus;
	int32_t NumTrades;
	MDLDoubleT<3> TotalVolumeTrade;
	MDLDoubleT<5> TotalValueTrade;
	MDLDoubleT<3> TotalBidQty;
	MDLDoubleT<3> TotalOfferQty;
	int32_t WithdrawBuyNumber;
	MDLDoubleT<3> WithdrawBuyAmount;
	int32_t WithdrawSellNumber;
	MDLDoubleT<3> WithdrawSellAmount;
	struct BidLevelsItem {
		MDLDoubleT<3> OrderQty;
		int32_t NumOrders;
		struct NoOrdersItem {
			MDLDoubleT<3> OrderQty;
		};
		MDLListT<NoOrdersItem> NoOrders;
	};
	MDLListT<BidLevelsItem> BidLevels;
	struct OfferLevelsItem {
		MDLDoubleT<3> OrderQty;
		int32_t NumOrders;
		struct NoOrdersItem {
			MDLDoubleT<3> OrderQty;
		};
		MDLListT<NoOrdersItem> NoOrders;
	};
	MDLListT<OfferLevelsItem> OfferLevels;
};

struct ATPTransaction {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_ATPTransaction
	};
	int32_t DataStatus;
	int32_t TradeIndex;
	int32_t TradeChannel;
	MDLAnsiString SecurityID;
	MDLTime TradeTime;
	MDLFloatT<3> TradePrice;
	MDLDoubleT<3> TradeQty;
	MDLDoubleT<5> TradeMoney;
	int64_t TradeBuyNo;
	int64_t TradeSellNo;
	MDLAnsiString TradeBSFlag;
};

struct SHL2Transaction2 {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_SHL2Transaction2
	};
	int32_t DataStatus;
	int32_t TradeIndex;
	int32_t TradeChan;
	MDLAnsiString SecurityID;
	MDLTime TradTime;
	MDLFloatT<3> TradPrice;
	MDLDoubleT<3> TradVolume;
	MDLDoubleT<5> TradeMoney;
	int64_t TradeBuyNo;
	int64_t TradeSellNo;
	MDLAnsiString TradeBSFlag;
	int64_t BizIndex;
	struct ExtraFieldsItem {
		int64_t Value;
	};
	MDLListT<ExtraFieldsItem> ExtraFields;
};

struct Order {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_Order
	};
	int32_t DataStatus;
	int32_t OrderIndex;
	int32_t OrderChannel;
	MDLAnsiString SecurityID;
	MDLTime OrderTime;
	MDLAnsiString OrderType;
	int64_t OrderNO;
	MDLFloatT<3> OrderPrice;
	MDLDoubleT<3> Balance;
	MDLAnsiString OrderBSFlag;
	int64_t BizIndex;
	struct ExtraFieldsItem {
		int64_t Value;
	};
	MDLListT<ExtraFieldsItem> ExtraFields;
};

struct BondMarketData {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_BondMarketData
	};
	MDLTime DataTime;
	int32_t DataStatus;
	MDLAnsiString SecurityID;
	int32_t ImageStatus;
	MDLFloatT<3> PreClosePx;
	MDLFloatT<3> OpenPx;
	MDLFloatT<3> HighPx;
	MDLFloatT<3> LowPx;
	MDLFloatT<3> LastPx;
	MDLFloatT<3> ClosePx;
	MDLAnsiString InstruStatus;
	int32_t NumTrades;
	int64_t TotalVolume;
	MDLDoubleT<3> TotalValue;
	int64_t TotalBidQty;
	MDLFloatT<3> AltWAvgBidPx;
	int64_t TotalOfferQty;
	MDLFloatT<3> AltWAvgOfferPx;
	MDLFloatT<3> WAvgPx;
	int32_t WDBuyNumber;
	int64_t WDBuyAmount;
	MDLDoubleT<3> WDBuyMoney;
	int32_t WDSellNumber;
	int64_t WDSellAmount;
	MDLDoubleT<3> WDSellMoney;
	int32_t TotalBidNum;
	int32_t TotalOfferNum;
	int32_t BidMaxDur;
	int32_t OfferMaxDur;
	int32_t BidOrderNo;
	int32_t OfferOrderNo;
	struct BidLevelsItem {
		MDLFloatT<3> Price;
		int64_t OrderQty;
		int32_t NumOrders;
	};
	MDLListT<BidLevelsItem> BidLevels;
	struct OfferLevelsItem {
		MDLFloatT<3> Price;
		int64_t OrderQty;
		int32_t NumOrders;
	};
	MDLListT<OfferLevelsItem> OfferLevels;
};

struct BondTick {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_BondTick
	};
	int32_t TickIndex;
	int32_t Channel;
	MDLAnsiString SecurityID;
	MDLTime TickTime;
	MDLAnsiString Type;
	int64_t BuyOrderNO;
	int64_t SellOrderNO;
	MDLFloatT<3> Price;
	int64_t Qty;
	MDLDoubleT<3> TradeMoney;
	MDLAnsiString TickBSFlag;
};

struct BondOrderQueue {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_BondOrderQueue
	};
	MDLTime DataTime;
	MDLAnsiString SecurityID;
	MDLAnsiString Side;
	MDLFloatT<3> Price;
	int64_t OrderQty;
	int32_t NumOrders;
	struct NoOrdersItem {
		int64_t OrderQty;
	};
	MDLListT<NoOrdersItem> NoOrders;
};

struct MarketInfo {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_MarketInfo
	};
	MDLAnsiString SecurityID;
	MDLTime UpdateTime;
	MDLUTF8String SecurityName;
	MDLAnsiString ObjectID;
	MDLAnsiString MarketClass;
	MDLAnsiString AssetClass;
	MDLAnsiString SubAssetClass;
	MDLAnsiString Currency;
	MDLDoubleT<3> FaceValue;
	MDLDate LastTradeDate;
	MDLDate ListDate;
	int32_t SetID;
	int64_t AskUnit;
	int64_t BidUnit;
	int64_t LLimitNum;
	int64_t ULimitNum;
	MDLFloatT<3> PreClosePrice;
	MDLFloatT<3> DeltaPriceUnit;
	MDLAnsiString LimitType;
	MDLFloatT<3> HighLimitPrice;
	MDLFloatT<3> LowLimitPrice;
	MDLFloatT<6> XRPercentage;
	MDLFloatT<6> XDMoney;
	MDLAnsiString FType;
	MDLAnsiString SType;
	MDLAnsiString Status;
	int64_t LMktPNum;
	int64_t UMktNum;
	MDLUTF8String Note;
};

struct NGTSTick {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_NGTSTick
	};
	int64_t BizIndex;
	int32_t Channel;
	MDLAnsiString SecurityID;
	MDLTime TickTime;
	MDLAnsiString Type;
	int64_t BuyOrderNO;
	int64_t SellOrderNO;
	MDLFloatT<3> Price;
	int64_t Qty;
	MDLDoubleT<3> TradeMoney;
	MDLAnsiString TickBSFlag;
};

struct ETFMarketData {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_ETFMarketData
	};
	MDLTime DataTime;
	MDLAnsiString SecurityID;
	int32_t ETFBuyNumber;
	MDLDoubleT<3> ETFBuyAmount;
	MDLDoubleT<3> ETFBuyMoney;
	int32_t ETFSellNumber;
	MDLDoubleT<3> ETFSellAmount;
	MDLDoubleT<3> ETFSellMoney;
};

struct BondDist {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_BondDist
	};
	MDLTime UpdateTime;
	MDLAnsiString SecurityID;
	int32_t ImageStatus;
	MDLFloatT<3> PreCloPrice;
	MDLFloatT<3> OpenPrice;
	MDLFloatT<3> HighPrice;
	MDLFloatT<3> LowPrice;
	MDLFloatT<3> LastPrice;
	MDLFloatT<3> ClosePrice;
	MDLAnsiString InstruStatus;
	uint32_t TradNumber;
	MDLDoubleT<3> TradVolume;
	MDLDoubleT<5> Turnover;
	MDLDoubleT<3> TotalBidVol;
	MDLFloatT<3> WAvgBidPri;
	MDLFloatT<3> AltWAvgBidPri;
	MDLDoubleT<3> TotalAskVol;
	MDLFloatT<3> WAvgAskPri;
	MDLFloatT<3> AltWAvgAskPri;
	uint32_t EtfBuyNumber;
	MDLDoubleT<3> EtfBuyVolume;
	MDLDoubleT<5> EtfBuyMoney;
	uint32_t EtfSellNumber;
	MDLDoubleT<3> EtfSellVolume;
	MDLDoubleT<5> ETFSellMoney;
	MDLFloatT<4> YieldToMatu;
	MDLDoubleT<3> TotWarExNum;
	MDLDoubleT<3> WarLowerPri;
	MDLDoubleT<5> WarUpperPri;
	uint32_t WiDBuyNum;
	MDLDoubleT<3> WiDBuyVol;
	MDLDoubleT<5> WiDBuyMon;
	uint32_t WiDSellNum;
	MDLDoubleT<3> WiDSellVol;
	MDLDoubleT<5> WiDSellMon;
	uint32_t TotBidNum;
	uint32_t TotSellNum;
	int32_t MaxBidDur;
	int32_t MaxSellDur;
	uint32_t BidNum;
	uint32_t SellNum;
	struct BidLevelsItem {
		MDLFloatT<3> Price;
		MDLDoubleT<3> Volume;
		uint32_t OrderNum;
	};
	MDLListT<BidLevelsItem> BidLevels;
	struct SellLevelsItem {
		MDLFloatT<3> Price;
		MDLDoubleT<3> Volume;
		uint32_t OrderNum;
	};
	MDLListT<SellLevelsItem> SellLevels;
	MDLFloatT<3> IOPV;
};

struct BondDistOrderQueue {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_BondDistOrderQueue
	};
	MDLTime DataTime;
	MDLAnsiString SecurityID;
	MDLAnsiString Side;
	MDLFloatT<3> Price;
	int64_t OrderQty;
	int32_t NumOrders;
	struct NoOrdersItem {
		int64_t OrderQty;
	};
	MDLListT<NoOrdersItem> NoOrders;
};

struct BondDistTick {
	enum {
		ServiceID = MDLSID_MDL_SHL2,
		ServiceVer = MDLVID_MDL_SHL2,
		MessageID = MDLMID_MDL_SHL2_BondDistTick
	};
	int64_t BizIndex;
	int32_t Channel;
	MDLAnsiString SecurityID;
	MDLTime TickTime;
	MDLAnsiString Type;
	int64_t BuyOrderNO;
	int64_t SellOrderNO;
	MDLFloatT<3> Price;
	int64_t Qty;
	MDLDoubleT<3> TradeMoney;
	MDLAnsiString TickBSFlag;
};

#pragma pack()

} // namespace mdl_shl2_msg
} // namespace mdl
} // namespace datayes
