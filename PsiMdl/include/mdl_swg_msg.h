// Generated by the code_gen tool.  DO NOT EDIT!
#pragma once

#include "mdl_api_types.h"

namespace datayes {
namespace mdl {
namespace mdl_swg_msg {

static const uint16_t MDLVID_MDL_SWG = 101;

enum MDL_SWGMessageID {
	MDLMID_MDL_SWG_Index = 1,
	MDLMID_MDL_SWG_HuobiMarketData = 2,
	MDLMID_MDL_SWG_HuobiTrade = 3,
	MDLMID_MDL_SWG_HuobiOrder = 4,
	MDLMID_MDL_SWG_HuobiMinuteBar = 5
};

#pragma pack(1)

struct Index {
	enum {
		ServiceID = MDLSID_MDL_SWG,
		ServiceVer = MDLVID_MDL_SWG,
		MessageID = MDLMID_MDL_SWG_Index
	};
	MDLAnsiString SecurityID;
	MDLUTF8String SecurityName;
	MDLFloatT<3> PreCloPrice;
	MDLFloatT<3> OpenPrice;
	uint64_t Turnover;
	MDLFloatT<3> HighPrice;
	MDLFloatT<3> LowPrice;
	MDLFloatT<3> LastPrice;
	MDLFloatT<3> Ratio;
	uint64_t Volume;
	uint64_t BidVolume123;
	MDLFloatT<3> BidPrice2;
	uint64_t BidVolume2;
	MDLFloatT<3> BidPrice3;
	uint64_t BidVolume3;
	uint64_t AskVolume123;
	MDLFloatT<3> AskPrice2;
	uint64_t AskVolume2;
	MDLFloatT<3> AskPrice3;
	uint64_t AskVolume3;
	MDLTime UpdateTime;
};

struct HuobiMarketData {
	enum {
		ServiceID = MDLSID_MDL_SWG,
		ServiceVer = MDLVID_MDL_SWG,
		MessageID = MDLMID_MDL_SWG_HuobiMarketData
	};
	MDLAnsiString Instrument;
	MDLDate UpdateDate;
	MDLTime UpdateTime;
	MDLDate LastTradeDate;
	MDLTime LastTradeTime;
	int64_t LastTradeID;
	MDLDoubleT<6> Volume;
	MDLDoubleT<6> OpenPrice;
	MDLDoubleT<6> LastPrice;
	MDLDoubleT<6> LowPrice;
	MDLDoubleT<6> HighPrice;
	MDLDoubleT<6> NewPrice;
	MDLDoubleT<6> Total;
	int64_t Amp;
	int32_t Level;
	struct AskPriceLevelItem {
		MDLDoubleT<6> Price;
		MDLDoubleT<6> Volume;
		int32_t Level;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
	struct BidPriceLevelItem {
		MDLDoubleT<6> Price;
		MDLDoubleT<6> Volume;
		int32_t Level;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
};

struct HuobiTrade {
	enum {
		ServiceID = MDLSID_MDL_SWG,
		ServiceVer = MDLVID_MDL_SWG,
		MessageID = MDLMID_MDL_SWG_HuobiTrade
	};
	MDLAnsiString Instrument;
	int64_t ID;
	int64_t TradeID;
	MDLDate TradeDate;
	MDLTime TradeTime;
	MDLTime Time;
	MDLDoubleT<6> Price;
	MDLDoubleT<6> Volume;
	int32_t Type;
	int32_t Direction;
};

struct HuobiOrder {
	enum {
		ServiceID = MDLSID_MDL_SWG,
		ServiceVer = MDLVID_MDL_SWG,
		MessageID = MDLMID_MDL_SWG_HuobiOrder
	};
	MDLAnsiString Instrument;
	int64_t ID;
	MDLDate OrderDate;
	MDLTime OrderTime;
	struct AskPriceLevelItem {
		MDLDoubleT<6> Price;
		MDLDoubleT<6> Volume;
	};
	MDLListT<AskPriceLevelItem> AskPriceLevel;
	struct BidPriceLevelItem {
		MDLDoubleT<6> Price;
		MDLDoubleT<6> Volume;
	};
	MDLListT<BidPriceLevelItem> BidPriceLevel;
};

struct HuobiMinuteBar {
	enum {
		ServiceID = MDLSID_MDL_SWG,
		ServiceVer = MDLVID_MDL_SWG,
		MessageID = MDLMID_MDL_SWG_HuobiMinuteBar
	};
	MDLAnsiString Instrument;
	int32_t Unit;
	MDLDate BarDate;
	MDLTime BarTime;
	MDLDoubleT<6> OpenPrice;
	MDLDoubleT<6> ClosePrice;
	MDLDoubleT<6> HighPrice;
	MDLDoubleT<6> LowPrice;
	MDLDoubleT<6> Volume;
};

#pragma pack()

} // namespace mdl_swg_msg
} // namespace mdl
} // namespace datayes
