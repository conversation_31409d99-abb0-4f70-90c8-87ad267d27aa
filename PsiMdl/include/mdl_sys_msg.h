// Generated by the code_gen tool.  DO NOT EDIT!
#pragma once

#include "mdl_api_types.h"

namespace datayes {
namespace mdl {
namespace mdl_sys_msg {

static const uint16_t MDLVID_MDL_SYS = 101;

enum MDL_SYSMessageID {
	MDLMID_MDL_SYS_Logon = 1,
	MDLMID_MDL_SYS_LogonResponse = 2,
	MDLMID_MDL_SYS_Logout = 3,
	MDLMID_MDL_SYS_Heartbeat = 4,
	MDLMID_MDL_SYS_ServiceStatus = 5,
	MDLMID_MDL_SYS_SessionStatus = 6,
	MDLMID_MDL_SYS_Logon2 = 7,
	MDLMID_MDL_SYS_FeederStatus = 8,
	MD<PERSON>ID_MDL_SYS_DataAPIRequest = 9,
	MDLMID_MDL_SYS_DataAPIResponse = 10,
	MDLMID_MDL_SYS_DataAPIRequest2 = 11,
	MDLMID_MDL_SYS_DataAPIResponse2 = 12,
	MDLMID_MDL_SYS_MessageStatus = 13,
	MDLMID_MDL_SYS_PublishRequest = 14,
	MDLMID_MDL_SYS_PublishResponse = 15,
	MDLMID_MDL_SYS_LogonAuth = 16,
	MDLMID_MDL_SYS_AuthResponse = 17,
	MDLMID_MDL_SYS_MsgRebuildRequest = 18,
	MDLMID_MDL_SYS_MsgRebuildResponse = 19,
	MDLMID_MDL_SYS_ConnCheckRequest = 20,
	MDLMID_MDL_SYS_ConnCheckResponse = 21,
	MDLMID_MDL_SYS_SubscribeRequest = 22,
	MDLMID_MDL_SYS_SubscribeResponse = 23,
	MDLMID_MDL_SYS_MCRecoverRequest = 24,
	MDLMID_MDL_SYS_MCRecoverPacket = 25,
	MDLMID_MDL_SYS_MCRecoverResponse = 26,
	MDLMID_MDL_SYS_MCSeqReset = 27,
	MDLMID_MDL_SYS_MCServerInfo = 28,
	MDLMID_MDL_SYS_MCLatestSeq = 29
};

#pragma pack(1)

struct Logon {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_Logon
	};
	MDLAnsiString UserName;
	MDLAnsiString Password;
	struct ServicesItem {
		uint32_t ServiceID;
		uint32_t ServiceVersion;
		struct MessagesItem {
			uint32_t MessageID;
		};
		MDLListT<MessagesItem> Messages;
	};
	MDLListT<ServicesItem> Services;
	uint32_t MessageEncoding;
	uint32_t HeartbeatInterval;
	uint32_t HeartbeatTimeout;
	uint32_t ClientAPIVersion;
};

struct LogonResponse {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_LogonResponse
	};
	MDLAnsiString UserName;
	MDLAnsiString Password;
	struct ServicesItem {
		uint32_t ServiceID;
		uint32_t ServiceVersion;
		struct MessagesItem {
			uint32_t MessageID;
			uint32_t MessageStatus;
		};
		MDLListT<MessagesItem> Messages;
	};
	MDLListT<ServicesItem> Services;
	uint32_t ReturnCode;
};

struct Logout {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_Logout
	};
	uint32_t LogoutCode;
};

struct Heartbeat {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_Heartbeat
	};
	uint32_t Reversed;
};

struct ServiceStatus {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_ServiceStatus
	};
	uint32_t Version;
	MDLDate StartDate;
	MDLTime StartTime;
	uint64_t MemoryTotal;
	uint64_t MemoryRSS;
	uint64_t MemoryPeak;
	uint32_t ClientCount;
	uint32_t SendRate;
	struct ServicesItem {
		uint32_t ServiceID;
		uint32_t ReceiveRate;
		uint32_t IdleTime;
	};
	MDLListT<ServicesItem> Services;
	uint64_t BytesDelayed;
};

struct SessionStatus {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_SessionStatus
	};
	struct ClientsItem {
		uint32_t Version;
		MDLAnsiString Address;
		MDLDate StartDate;
		MDLTime StartTime;
		uint32_t EncodeType;
		uint32_t DecodeType;
		uint32_t BytesDelayed;
		MDLAnsiString SubscriptionList;
	};
	MDLListT<ClientsItem> Clients;
};

struct Logon2 {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_Logon2
	};
	MDLAnsiString UserName;
	MDLAnsiString Password;
	struct ServicesItem {
		uint32_t ServiceID;
		uint32_t ServiceVersion;
		struct MessagesItem {
			uint32_t MessageID;
			MDLAnsiString FieldName;
			struct FieldValuesItem {
				MDLAnsiString FieldValue;
			};
			MDLListT<FieldValuesItem> FieldValues;
		};
		MDLListT<MessagesItem> Messages;
	};
	MDLListT<ServicesItem> Services;
	uint32_t MessageEncoding;
	uint32_t HeartbeatInterval;
	uint32_t HeartbeatTimeout;
	uint32_t ClientAPIVersion;
};

struct FeederStatus {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_FeederStatus
	};
	uint32_t ServiceNum;
	MDLDate StartDate;
	MDLTime StartTime;
	uint32_t FileCount;
	uint64_t FileSize;
	uint64_t DiskFree;
	uint32_t ErrorCount;
	uint32_t WarnCount;
	MDLAnsiString Notes;
	uint32_t UpStreamStatus;
	struct MessagesItem {
		uint32_t MessageID;
		MDLTime LastTime;
		uint64_t MessageCount;
	};
	MDLListT<MessagesItem> Messages;
};

struct DataAPIRequest {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_DataAPIRequest
	};
	MDLUTF8String URI;
};

struct DataAPIResponse {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_DataAPIResponse
	};
	uint32_t ReturnCode;
	uint32_t CompressEncoding;
	uint32_t OrignalSize;
	uint32_t Charset;
	MDLUTF8String ResultSet;
};

struct DataAPIRequest2 {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_DataAPIRequest2
	};
	MDLUTF8String URI;
};

struct DataAPIResponse2 {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_DataAPIResponse2
	};
	uint32_t ReturnCode;
	uint32_t CompressEncoding;
	uint32_t OrignalSize;
	uint32_t ResultSetSize;
	uint32_t Charset;
	struct ResultSetItem {
		uint32_t Item;
	};
	MDLListT<ResultSetItem> ResultSet;
};

struct MessageStatus {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_MessageStatus
	};
	MDLTime UpdateTime;
	struct ServicesItem {
		uint32_t ServiceID;
		struct StreamsItem {
			uint32_t StreamID;
			int32_t LocalLatency;
			int32_t Latency;
			uint32_t IdleTime;
			uint32_t BytesThroughtput;
			uint32_t MessageThroughtput;
		};
		MDLListT<StreamsItem> Streams;
	};
	MDLListT<ServicesItem> Services;
};

struct PublishRequest {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_PublishRequest
	};
	uint32_t SvrID;
	uint32_t MsgID;
	uint32_t ChannelID;
	uint64_t SequenceBegin;
	uint64_t SequenceEnd;
};

struct PublishResponse {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_PublishResponse
	};
	uint32_t SvrID;
	uint32_t MsgID;
	uint32_t ChannelID;
	uint64_t SequenceBegin;
	uint64_t SequenceEnd;
	uint32_t ReturnCode;
	uint32_t RetryFlag;
};

struct LogonAuth {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_LogonAuth
	};
	uint32_t AuthType;
	MDLAnsiString AuthText;
};

struct AuthResponse {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_AuthResponse
	};
	uint32_t AuthType;
	uint32_t AuthResult;
	MDLAnsiString Description;
};

struct MsgRebuildRequest {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_MsgRebuildRequest
	};
	uint32_t SvrID;
	uint32_t MsgID;
	uint64_t SeqBegin;
	uint64_t SeqEnd;
	uint64_t ChannelID;
};

struct MsgRebuildResponse {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_MsgRebuildResponse
	};
	uint32_t SvrID;
	uint32_t MsgID;
	uint64_t SeqBegin;
	uint64_t SeqCur;
	uint64_t SeqEnd;
	uint64_t ChannelID;
	uint32_t ReturnCode;
	uint32_t RetryFlag;
};

struct ConnCheckRequest {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_ConnCheckRequest
	};
	uint32_t Index;
	MDLAnsiString UserName;
};

struct ConnCheckResponse {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_ConnCheckResponse
	};
	uint32_t Code;
	uint32_t Index;
	uint32_t ServerLoad;
	MDLAnsiString Payload;
};

struct SubscribeRequest {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_SubscribeRequest
	};
	struct ServicesItem {
		uint32_t ServiceID;
		uint32_t ServiceVersion;
		struct MessagesItem {
			uint32_t MessageID;
			MDLAnsiString FieldName;
			struct FieldValuesItem {
				MDLAnsiString FieldValue;
			};
			MDLListT<FieldValuesItem> FieldValues;
		};
		MDLListT<MessagesItem> Messages;
	};
	MDLListT<ServicesItem> Services;
};

struct SubscribeResponse {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_SubscribeResponse
	};
	struct ServicesItem {
		uint32_t ServiceID;
		uint32_t ServiceVersion;
		struct MessagesItem {
			uint32_t MessageID;
			uint32_t MessageStatus;
		};
		MDLListT<MessagesItem> Messages;
	};
	MDLListT<ServicesItem> Services;
};

struct MCRecoverRequest {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_MCRecoverRequest
	};
	struct MessagesItem {
		uint32_t MsgType;
		struct RangesItem {
			uint64_t FromSeq;
			uint64_t ToSeq;
		};
		MDLListT<RangesItem> Ranges;
	};
	MDLListT<MessagesItem> Messages;
};

struct MCRecoverPacket {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_MCRecoverPacket
	};
	uint64_t RequestId;
	MDLAnsiString PacketData;
};

struct MCRecoverResponse {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_MCRecoverResponse
	};
	struct MessagesItem {
		uint32_t MsgType;
		struct RangesItem {
			uint64_t FromSeq;
			uint64_t ToSeq;
			uint32_t Result;
		};
		MDLListT<RangesItem> Ranges;
	};
	MDLListT<MessagesItem> Messages;
};

struct MCSeqReset {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_MCSeqReset
	};
	struct MsgTypesItem {
		uint32_t Type;
	};
	MDLListT<MsgTypesItem> MsgTypes;
};

struct MCServerInfo {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_MCServerInfo
	};
	uint32_t ProcessId;
};

struct MCLatestSeq {
	enum {
		ServiceID = MDLSID_MDL_SYS,
		ServiceVer = MDLVID_MDL_SYS,
		MessageID = MDLMID_MDL_SYS_MCLatestSeq
	};
	struct MsgTypesItem {
		uint32_t Type;
		uint64_t Seq;
	};
	MDLListT<MsgTypesItem> MsgTypes;
};

#pragma pack()

} // namespace mdl_sys_msg
} // namespace mdl
} // namespace datayes
