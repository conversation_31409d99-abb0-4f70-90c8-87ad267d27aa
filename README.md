# 项目架构文档

## 总体架构概述

该项目是一个基于TCP或共享内存（SHM）的持久化消息队列框架，适用于Linux系统。它提供了一个可靠且高效的消息传输机制，通过序列号和确认机制确保发送的消息在接收方被处理。此外，共享内存通信提供了比TCP更快的本地通信速度。


## 部署

clion 导入CmakeLists.txt，编译运行即可。<p>
运行之前请修改config.yaml中的配置。

## 开发

1、需要客户端服务端交互的，msg.h里新增消息类型，继承MsgTpl<msg_id>，自定义请求和响应结构体。
2、分别实现客户端与服务端
    - 客户端：继承TcpShmClient，实现具体的发送和接收逻辑，调用handleMsg处理服务端响应。
        - 发送：调用TrySendMsg发送消息，对于动态分配，请手动调用Alloc()和Push()。(Poll)
        - 接收：调用handleMsg处理服务端响应。(OnServerMsg)
    - 服务端：继承TcpShmServer，实现具体的连接处理、消息接收和发送逻辑，调用handleMsg处理客户端请求。
        - 接收与处理响应：调用OnClientMsg处理客户端请求，根据不同的MsgType调用不同的函数。
3、对于其他功能，请写在Run()函数里，根据需求做出相应的修改。

注意点：
1、Connection为线程不安全的单消费者，单生产者模型，请不要在多个线程中同时操作同一个Connection。
2、尚未完成的配置项为TcpQueueSize，需要修改代码，位于PsiHttpShm/PsiTcpShmConf.h。
3、默认情况下，客户端与服务端之间维持长连接，需要断开请Close()。

## 项目架构与流程图

### 流程图
![](./doc/客户端服务端流程图.png)

### 消息体格式
![](./doc/msgFormat.png)

### TcpShmConn
![](./doc/TcpShmConn.png)

## 技术栈

* 编程语言：C++
* 通信协议：TCP/IP, 共享内存（SHM）
* 系统调用：socket, mmap, shm_open
* 其他工具：POSIX线程（pthread）

## 设计决策

* **持久化消息队列** ：通过持久化消息队列确保消息在系统崩溃后仍能恢复。
* **共享内存通信** ：提供比TCP更快的本地通信速度。
* **非阻塞I/O** ：使用非阻塞I/O和轮询机制以降低延迟。
* **线程模型** ：支持自定义线程模型，允许用户根据需求配置线程数量和任务分配。

## 组件

### TcpShmConnection

* **目的** ：封装TCP和SHM连接，提供消息发送和接收的接口。
* **关键数据结构** ：`MsgHeader`，`SPSCVarQueue`，`PTCPConnection`。
* **设计模式** ：模板类，提供灵活的配置选项。

### TcpShmServer

* **目的** ：实现TCP/SHM服务器，处理新连接、消息接收和发送。
* **关键数据结构** ：`ConnectionGroup`，`NewConn`。
* **设计模式** ：模板类，支持自定义配置和回调函数。

### TcpShmClient

* **目的** ：实现TCP/SHM客户端，连接服务器并进行消息通信。
* **关键数据结构** ：`LoginMsg`，`LoginRspMsg`。
* **设计模式** ：模板类，支持自定义配置和回调函数。

## 代码

### 目录结构
```

.
├── client.bak 客户端备份代码，用于编写多个实例
├── client.cpp 客户端
├── CMakeLists.txt
├── config.yaml 配置文件
├── Makefile make生成可执行文件
├── msg.h 消息文件，请提前定义好
├── PsiHttpShm 
│   ├── internal
│   ├── PsiMmap.h mmap封装
│   ├── PsiMsgHeader.h 基础消息头文件
│   ├── PsiTcpShmClient.h 客户端封装
│   ├── PsiTcpShmClient.tpp
│   ├── PsiTcpShmConf.h 配置类
│   ├── PsiTcpShmConn.h 连接类
│   ├── PsiTcpShmConn.tpp
│   ├── PsiTcpShmServer.h 服务端封装
│   └── PsiTcpShmServer.tpp
├── PsiMdl MDL订阅
│   ├── 3rd
│   ├── include
│   ├── libs
│   ├── PsiMdlApiConf.h
│   └── PsiMdlApi.h
├── README.md
├── server.bak
└── server.cpp  服务端
```

对于客户端消息处理，请根据MDL定义的MessageID和ServiveID，在对应的handleMsg中处理。

### API概述

* **服务器API** ：`Start`，`Stop`，`PollCtl`，`PollTcp`，`PollShm`。
* **客户端API** ：`Connect`，`PollTcp`，`PollShm`，`Stop`。
* **连接API** ：`Alloc`，`Push`，`Front`，`Pop`，`Close`。



## 代码示例

客户端：

```c++
using ClientConf = ClientNormalConf;//设置配置，using默认或自定义配置
class EchoClient;
using TSClient = TcpShmClient<EchoClient, ClientConf>;
                  ↓ 消息type，通过这个来区分不同种类消息
struct Msg1:MsgTpl<1> {
    int val;//这里的值可以随意设置，样例中是int，val名不能改变，可以是任意固定长度的，比如char[10];
};//继承模板类，定义消息体

class Client : public TSClient//1、继承模板类，使用方法和属性 2、作为模板类参数，定制TcpShmclient的行为
{
public:
    void Run(bool use_shm, const char* server_ipv4, uint16_t server_port);
    基本不用修改，里面是轮询消息队列，接收服务端消息处理回调等

private:
    bool Poll()//具体的发送逻辑

    template<class T>
    bool TrySendMsg() //基本不用改，发送消息，对于动态分配，请手动调用Alloc()和Push()

    template<class T>
    void handleMsg(T* msg)//处理服务端响应的具体逻辑
private:
    void OnSystemError(const char* error_msg, int sys_errno) //服务错误时调用

    void OnLoginReject(const LoginRspMsg* login_rsp) //登录错误时调用

    int64_t OnLoginSuccess(const LoginRspMsg* login_rsp) //登录成功时调用

    void OnServerMsg(MsgHeader* header) //收到服务器请求的具体逻辑，调用handleMsg

    void OnDisconnected(const char* reason, int sys_errno) //断连回调
};

int main() {
    YAML::Node config = YAML::LoadFile("config.yaml");
    ClientConf client_conf;
    parseClientConf(config["TcpShm"],client_conf);

    std::string ptcp_dir = client_conf.PrefixPTCPFolder;
    mkdir(ptcp_dir.c_str(), 0755);
    ptcp_dir += client_conf.ClientName;
    mkdir(ptcp_dir.c_str(), 0755);

    Client client(ptcp_dir, client_conf.ClientName,client_conf);
    client.Run(client_conf.UseShm, client_conf.ServerAddr.c_str(), 12345);
    return 0;
}
```

服务端编写：

```c++

class Server : public TSServer
{
public:

    void Run(const char* listen_ipv4, uint16_t listen_port);

private:
    friend TSServer;

    void OnSystemError(const char* errno_msg, int sys_errno);//系统错误时调用

    int OnNewConnection(const struct sockaddr_in& addr, const LoginMsg* login, LoginRspMsg* login_rsp)//建立新连接回调

    void OnClientFileError(Connection& conn, const char* reason, int sys_errno);

    void OnClientLogon(const struct sockaddr_in& addr, Connection& conn);//客户端登录

    void OnClientDisconnected(Connection& conn, const char* reason, int sys_errno);//客户端断连

    void OnClientMsg(Connection& conn, MsgHeader* recv_header);//收到客户端消息
};
int main() {
    YAML::Node config = YAML::LoadFile("config.yaml");
    ServerConf server_conf;
    parseServerConf(config["TcpShm"],server_conf);
    parseMdlApiConf(config["MdlApi"],mdl_conf);

    Server server("Myserver", server_conf.ServerName,server_conf);
    server.Run("0.0.0.0", server_conf.ListenPort);
    return 0;
}
```

关于使用到的TcpShmConn API接口:

```c++

    /**
     * 获取 PTCP 文件的路径
     * @return PTCP 文件的路径
     */
    std::string GetPtcpFile();
    /**
     * 判断连接是否已关闭
     * @return 连接已关闭返回 true，否则返回 false
     */
    bool IsClosed();

    /**
     * 关闭连接
     */
    void Close();

    /**
     * 获取连接关闭原因
     * @param sys_errno 存储系统错误号的指针
     * @return 关闭原因字符串
     */
    const char* GetCloseReason(int* sys_errno);

    /**
     * 获取远程名称
     * @return 远程名称字符串
     */
    char* GetRemoteName();

    /**
     * 获取本地名称
     * @return 本地名称字符串
     */
    const char* GetLocalName();

    /**
     * 获取 PTCP 目录
     * @return PTCP 目录字符串
     */
    const char* GetPtcpDir();

    /**
     * 分配一个指定大小的消息
     * @param size 消息大小
     * @return 分配的消息头指针，如果空间不足则返回 nullptr
     */
    MsgHeader* Alloc(uint16_t size);

    /**
     * 提交最后一个消息来自Alloc()并发送
     */
    void Push();

    /**
     * 提交更多消息
        *   对于shm,和Push相同
        *   对于tcp,不立即发送，因为我们还有更多消息要推送
     */ 
    void PushMore();

    /**
     * 获取下一个消息
     * @return 下一个消息头指针，如果队列为空则返回 nullptr，返回地址保证8字节对齐
     */
    MsgHeader* Front();

    /**
     * 消费从Front()或者polling函数获取的消息
     */
    void Pop();
```
由于seq依赖于消息队列，无法离开消息队列单独进行发送，PushRaw，AllocRaw接收端还需要进行单独处理，如果不需要seq机制，在login时，关闭seq验证。
