#include <cassert>
#include <string>
#include<iostream>
#include <thread>
#include "./PsiHttpShm/PsiTcpShmCommon.h"
#include "./msg.h"
#include "./PsiHttpShm/PsiTcpShmClient.h"
#include "nlohmann/json.hpp"

using namespace  std;
using namespace tcpshm;

class Client;
using TSClient = TcpShmClient<Client>;

int cnt = 0;

class Client : public TSClient//1、继承模板类，使用方法属性 2、作为模板类参数，定制TcpShmclient的行为
{
public:
    Client(const std::string& ptcp_dir, const std::string& name,const ClientConf& client_conf)
        : TSClient(ptcp_dir, name,client_conf)
        , conn(GetConnection()) {
        srand(time(NULL));
    }

    void Run(bool use_shm, const char* server_ipv4, uint16_t server_port) {
        if(!Connect(use_shm, server_ipv4, server_port, 0)) return;
        function<void()> poll_func;
        if(use_shm) {
            poll_func = [this]() {
                if(Poll()) {
                    conn.Close();
                }
                PollShm();
            };
        }else {
            poll_func=[this]() {
                if(Poll()) {
                    conn.Close();
                }
            };
        }
        std::thread thr([this,poll_func]() {
            while(!conn.IsClosed()) {
                poll_func();
            }
        });
        while(!conn.IsClosed()) {
            PollTcp(now());
        }
        thr.join();
    }

private:
    // 请求
    bool Poll() {
         // 发送ping请求
        sendPing();
        return true;
    }

    void sendPing() {
        conn.TrySendMsg<PingReq>([](PingReq* req) {
            strcpy(req->val, "ping");
        });
        cout<<"send ping"<<endl;
    }
    // 响应
    void handlePing(const PingResp* ping_resp) {
         cout << "Got ping response, seq: " << ping_resp->val << endl;
    }

private:
    friend TSClient;
    // called within Connect()
    // reporting errors on connecting to the server
    void OnSystemError(const char* error_msg, int sys_errno) {
        cout << "System Error: " << error_msg << " syserrno: " << strerror(sys_errno) << endl;
    }

    // called within Connect()
    // Login rejected by server
    void OnLoginReject(const LoginRspMsg* login_rsp) {
        cout << "Login Rejected: " << login_rsp->error_msg << endl;
    }

    // called within Connect()
    // confirmation for login success
    int64_t OnLoginSuccess(const LoginRspMsg* login_rsp) {
        cout << "Login Success" << endl;
        return now();
    }

    // called by tcp thread
    void OnDisconnected(const char* reason, int sys_errno) {
        cout << "Client disconnected reason: " << reason << " syserrno: " << strerror(sys_errno) << endl;
    }

    // called within Connect()
    // server and client ptcp sequence number don't match, we need to fix it manually
    void OnSeqNumberMismatch(uint32_t local_ack_seq,
                             uint32_t local_seq_start,
                             uint32_t local_seq_end,
                             uint32_t remote_ack_seq,
                             uint32_t remote_seq_start,
                             uint32_t remote_seq_end) {
        cout << "Seq number mismatch, name: " << conn.GetRemoteName() << " ptcp file: " << conn.GetPtcpFile()
             << " local_ack_seq: " << local_ack_seq << " local_seq_start: " << local_seq_start
             << " local_seq_end: " << local_seq_end << " remote_ack_seq: " << remote_ack_seq
             << " remote_seq_start: " << remote_seq_start << " remote_seq_end: " << remote_seq_end << endl;
    }

    // called by APP thread
    void OnServerMsg(MsgHeader* header) {
        switch(header->msg_type) {
            case MSG_PING_TYPE: {
                handlePing(header->GetMsgBodyPtr<PingResp>());
                break;
            }
            default: {
                cout << "Unknown msg type: " << header->msg_type << endl;
                break;
            }
        }
        conn.Pop();
    }

private:
    Connection& conn;
};

int main() {
    YAML::Node config = YAML::LoadFile("config.yaml");
    ClientConf client_conf;
    parseClientConf(config["TcpShm"],client_conf);

    std::string ptcp_dir = client_conf.PrefixPTCPFolder;
    mkdir(ptcp_dir.c_str(), 0755);
    ptcp_dir += client_conf.ClientName;
    mkdir(ptcp_dir.c_str(), 0755);

    Client client(ptcp_dir, client_conf.ClientName,client_conf);
    client.Run(client_conf.UseShm, client_conf.ServerAddress.c_str(), 12345);
    return 0;
}