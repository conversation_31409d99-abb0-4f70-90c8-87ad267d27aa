#include <cassert>
#include <string>
#include<iostream>
#include <thread>
#include "./PsiHttpShm/PsiTcpShmConf.h"
#include "./msg.h"
#include "./PsiHttpShm/PsiTcpShmClient.h"
#include "nlohmann/json.hpp"

using namespace  std;
using namespace tcpshm;

class Client;
using TSClient = TcpShmClient<Client>;

int cnt = 0;

class Client : public TSClient//1、继承模板类，使用方法属性 2、作为模板类参数，定制TcpShmclient的行为
{
public:
    Client(const std::string& ptcp_dir, const std::string& name,const ClientConf& client_conf)
        : TSClient(ptcp_dir, name,client_conf)
        , conn(GetConnection()) {
        srand(time(NULL));
    }

    void Run(bool use_shm, const char* server_ipv4, uint16_t server_port) {
        if(!Connect(use_shm, server_ipv4, server_port, 0)) return;
        function<void()> poll_func;
        if(use_shm) {
            poll_func = [this]() {
                if(Poll()) {
                    conn.Close();
                }
                PollShm();
            };
        }else {
            poll_func=[this]() {
                if(Poll()) {
                    conn.Close();
                }
            };
        }
        std::thread thr([this,poll_func]() {
            while(!conn.IsClosed()) {
                poll_func();
            }
        });
        while(!conn.IsClosed()) {
            PollTcp(now());
        }
        thr.join();
    }

private:
    // 请求
    bool Poll() {
         // 发送ping请求
        // return sendPing();
        // sleep(1);
        // return false;
        return sendSub();
    }

    bool sendPing() {
        conn.TrySendMsg<PingReq>([](PingReq* req) {
            strcpy(req->val, "ping");
        });
        cout<<"send ping"<<endl;
        sleep(1);
        return false;
    }

    // 响应
    void handlePing(const PingResp* ping_resp) {
         cout << "Got ping response, seq: " << ping_resp->val << endl;
    }

    bool sendSub() {
        conn.TrySendMsg<SubReq>([](SubReq* req) {
            req->messageID = datayes::mdl::mdl_szl2_msg::Snapshot300111_v2::MessageID;
            req->serviceID = datayes::mdl::MDLServiceID::MDLSID_SZL2;
        });
        cout<<"send sub"<<endl;
        sleep(60*5);
        return false;
    }

    void handleSub(const SubResp* sub_resp) {
        cout << "result:"<<sub_resp<<endl;
    }

    void handleMdlMessage(MsgHeader* header) {
        size_t body_size = header->size - sizeof(MsgHeader);
        char* body = header->GetMsgBodyRawPtr();
        size_t offset = 0;
        int serviceID;
        memcpy(&serviceID, body + offset, sizeof(serviceID));
        offset += sizeof(serviceID);
        int messageID;
        memcpy(&messageID, body + offset, sizeof(messageID));
        offset += sizeof(messageID);
        // size_t mdl_msg_size = body_size - offset;
        // char* mdl_msg_body = body + offset;
        cout<<"serviceID: "<<serviceID<<" messageID: "<<messageID<<endl;
        switch (serviceID) {

        }
    }

private:
    friend TSClient;
    // called within Connect()
    // reporting errors on connecting to the server
    void OnSystemError(const char* error_msg, int sys_errno) {
        cout << "System Error: " << error_msg << " syserrno: " << strerror(sys_errno) << endl;
    }

    // called within Connect()
    // Login rejected by server
    void OnLoginReject(const LoginRspMsg* login_rsp) {
        cout << "Login Rejected: " << login_rsp->error_msg << endl;
    }

    // called within Connect()
    // confirmation for login success
    int64_t OnLoginSuccess(const LoginRspMsg* login_rsp) {
        cout << "Login Success" << endl;
        return now();
    }

    // called by tcp thread
    void OnDisconnected(const char* reason, int sys_errno) {
        cout << "Client disconnected reason: " << reason << " syserrno: " << strerror(sys_errno) << endl;
    }

    // called within Connect()
    // server and client ptcp sequence number don't match, we need to fix it manually
    void OnSeqNumberMismatch(uint32_t local_ack_seq,
                             uint32_t local_seq_start,
                             uint32_t local_seq_end,
                             uint32_t remote_ack_seq,
                             uint32_t remote_seq_start,
                             uint32_t remote_seq_end) {
        cout << "Seq number mismatch, name: " << conn.GetRemoteName() << " ptcp file: " << conn.GetPtcpFile()
             << " local_ack_seq: " << local_ack_seq << " local_seq_start: " << local_seq_start
             << " local_seq_end: " << local_seq_end << " remote_ack_seq: " << remote_ack_seq
             << " remote_seq_start: " << remote_seq_start << " remote_seq_end: " << remote_seq_end << endl;
    }

    // called by APP thread
    void OnServerMsg(MsgHeader* header) {
        switch(header->msg_type) {
            case MSG_PING_TYPE: {
                handlePing(header->GetMsgBodyPtr<PingResp>());
                break;
            }
            case MSG_SUB_TYPE: {
                handleSub(header->GetMsgBodyPtr<SubResp>());
                break;
            }
            case MSG_MDL_MESSAGE_TYPE: {
                handleMdlMessage(header);
                break;
            }
            default: {
                cout << "Unknown msg type: " << header->msg_type << endl;
                break;
            }
        }
        conn.Pop();
    }

private:
    Connection& conn;
};

int main() {
    YAML::Node config = YAML::LoadFile("config.yaml");
    ClientConf client_conf;
    parseClientConf(config["TcpShm"],client_conf);

    std::string ptcp_dir = client_conf.PrefixPTCPFolder;
    mkdir(ptcp_dir.c_str(), 0755);
    ptcp_dir += client_conf.ClientName;
    mkdir(ptcp_dir.c_str(), 0755);//TODO: 增加递归创建文件夹的方法

    Client client(ptcp_dir, client_conf.ClientName,client_conf);
    client.Run(client_conf.UseShm, client_conf.ServerAddr.c_str(), 12345);
    return 0;
}