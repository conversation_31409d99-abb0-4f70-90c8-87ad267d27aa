//
// Created by Fsyrl on 24-8-29.
//

#ifndef MSG_H
#define MSG_H
#include "./PsiHttpShm/PsiTcpShmConf.h"
#include "./PsiMdl/include/mdl_api_types.h"
#include "PsiMdl/include/mdl_api.h"
#include "PsiMdl/include/mdl_szl2_msg.h"
using namespace tcpshm;
//建议：一对req和resp算一个msg_type(会在客户端服务端分别处理，单独一次请求另作处理
//对于客户端，应构建新Msg，服务端根据需要处理
enum MsgType {
    MSG_PING_TYPE = 1,
    MSG_SUB_TYPE,
    MSG_UNSUB_TYPE,
    MSG_MDL_MESSAGE_TYPE,
};

//需要保证固定大小，以便于传输
//如果需要不固定的，请自行memset构造
struct PingReq:MsgTpl<MSG_PING_TYPE> {
    char val[10];
};//发送的结构体
struct PingResp:MsgTpl<MSG_PING_TYPE> {
    char val[10];
};//接收的结构体

struct SubReq:MsgTpl<MSG_SUB_TYPE> {
    datayes::mdl::MDLServiceID serviceID;
    int messageID;
};
struct SubResp:MsgTpl<MSG_SUB_TYPE> {
    char result[10];
};

struct UnsubReq:MsgTpl<MSG_UNSUB_TYPE> {
    datayes::mdl::MDLServiceID serviceID;
    int messageID;
};
struct UnsubResp:MsgTpl<MSG_UNSUB_TYPE> {
    char result[10];
};

inline int SubID(datayes::mdl::MDLServiceID serviceID, int messageID) {
    return (serviceID << 16) | messageID;
}
inline std::pair<datayes::mdl::MDLServiceID, int> ExSubID(int subID) {
    auto serviceID = static_cast<datayes::mdl::MDLServiceID>(subID >> 16);
    int messageID = subID & 0xFFFF;
    return std::make_pair(serviceID, messageID);
}
#endif //MSG_H
