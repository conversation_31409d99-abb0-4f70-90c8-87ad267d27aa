#include <bits/stdc++.h>
#include "./PsiHttpShm/PsiTcpShmServer.h"
#include "./PsiHttpShm/PsiTcpShmConf.h"
#include "./PsiMdl/PsiMdlApi.h"
#include "./PsiMdl/PsiMdlApiConf.h"
#include "./msg.h"
#include "nlohmann/json.hpp"
using namespace  std;
using namespace tcpshm;

class Server;
using TSServer = TcpShmServer<Server>;


class Server : public TSServer
{
public:
    Server(const std::string& ptcp_dir, const std::string& name,const ServerConf& conf)
        : TSServer(ptcp_dir,name, conf),conf_(conf) {
        // capture SIGTERM to gracefully stop the server
        // we can also send other signals to crash the server and see how it recovers on restart
        signal(SIGTERM, Server::SignalHandler);
    }

    static void SignalHandler(int s) {
        stopped = true;
    }

    void Run(const char* listen_ipv4, uint16_t listen_port) {
        if(!Start(listen_ipv4, listen_port)) return;
        vector<thread> threads;
        // create threads for polling tcp
        threads.reserve(conf_.MaxTcpGrps+conf_.MaxShmGrps);
        for(int i = 0; i < conf_.MaxTcpGrps; i++) {
          threads.emplace_back([this, i]() {
            while (!stopped) {
              PollTcp(now(), i);
            }
          });
        }

        // create threads for polling shm
        for(int i = 0; i < conf_.MaxShmGrps; i++) {
          threads.emplace_back([this, i]() {
            while (!stopped) {
              PollShm(i);
            }
          });
        }
        MyMessageHandler msgHandler;
        const  std::string mdl_endpoint = mdl_conf.ServerAddr+":"+ mdl_conf.ServerPort;
        msgHandler.SetCallback([this](int topic,const MDLMessage* mdl_msg) {
            CustomTcpPoll([&](Connection& conn) {
                if(conn.IsSubscribed(topic)) {
                    const auto &[serviceID, messageID] = ExSubID(topic);
                    size_t body_size=mdl_msg->GetBodySize()+sizeof(serviceID)+sizeof(messageID);
                    // MsgHeader* header = conn.AllocRaw(body_size);
                    MsgHeader* header=conn.Alloc(body_size);//线程不安全，可能主线程分配内存的时候调度。需要保证在订阅发送的时候，没有其他OnClientMsg的发送操作。
                    header->msg_type=MSG_MDL_MESSAGE_TYPE;
                    size_t offset = 0;
                    char* body = header->GetMsgBodyRawPtr();
                    memcpy(body, &serviceID, sizeof(serviceID));

                    offset += sizeof(serviceID);
                    memcpy(body+offset, &messageID, sizeof(messageID));
                    offset += sizeof(messageID);
                    memcpy(body+offset, mdl_msg->GetBody(), mdl_msg->GetBodySize());
                    conn.Push();
                    // conn.PushRaw(header);
                }
                    // MsgHeader* header=conn.Alloc(sizeof(PingResp));
                    // // MsgHeader *header=conn.AllocRaw(sizeof(PingResp));
                    // header->msg_type=MSG_PING_TYPE;
                    // auto* resp=header->GetMsgBodyPtr<PingResp>();
                    // strcpy(resp->val,"pong");
                    // // conn.PushRaw(header);
                    // conn.Push();

            });
        });//设置回调函数，遍历所有连接，发送指定请求。
        msgHandler.Open(mdl_endpoint.c_str());//这里面会创建其他线程

        while(!stopped) {
          PollCtl(now());
        }
        msgHandler.Close();
        for(auto& thr : threads) {
            thr.join();
        }
        Stop();
        cout << "Server stopped" << endl;
    }

private:
    friend TSServer;

    // called with Start()
    // reporting errors on Starting the server
    void OnSystemError(const char* errno_msg, int sys_errno) {
        cout << "System Error: " << errno_msg << " syserrno: " << strerror(sys_errno) << endl;
    }
    // called by CTL thread
    // if accept the connection, set user_data in login_rsp and return grpid(start from 0) with respect to tcp or shm
    // else set error_msg in login_rsp if possible, and return -1
    // Note that even if we accept it here, there could be other errors on handling the login,
    // so we have to wait OnClientLogon for confirmation
    int OnNewConnection(const struct sockaddr_in& addr, const LoginMsg* login, LoginRspMsg* login_rsp) {
        cout << "New Connection from: " << inet_ntoa(addr.sin_addr) << ":" << ntohs(addr.sin_port)
             << ", name: " << login->client_name << ", use_shm: " << (bool)login->use_shm << endl;
        // here we simply hash client name to uniformly map to each group
        auto hh = hash<string>{}(string(login->client_name));
        if(login->use_shm) {
            if(conf_.MaxShmGrps > 0) {
                return hh % conf_.MaxShmGrps;
            }
            else {
                strcpy(login_rsp->error_msg, "Shm disabled");
                return -1;
            }
        }
        else {
            if(conf_.MaxTcpGrps > 0) {
                return hh % conf_.MaxTcpGrps;
            }
            else {
                strcpy(login_rsp->error_msg, "Tcp disabled");
                return -1;
            }
        }
    }

    // called by CTL thread
    // ptcp or shm files can't be open or are corrupt
    void OnClientFileError(Connection& conn, const char* reason, int sys_errno) {
        cout << "Client file errno, name: " << conn.GetRemoteName() << " reason: " << reason
             << " syserrno: " << strerror(sys_errno) << endl;
    }

    // called by CTL thread
    // server and client ptcp sequence number don't match, we need to fix it manually
    void OnSeqNumberMismatch(Connection& conn,
                             uint32_t local_ack_seq,
                             uint32_t local_seq_start,
                             uint32_t local_seq_end,
                             uint32_t remote_ack_seq,
                             uint32_t remote_seq_start,
                             uint32_t remote_seq_end) {
        cout << "Client seq number mismatch, name: " << conn.GetRemoteName() << " ptcp file: " << conn.GetPtcpFile()
             << " local_ack_seq: " << local_ack_seq << " local_seq_start: " << local_seq_start
             << " local_seq_end: " << local_seq_end << " remote_ack_seq: " << remote_ack_seq
             << " remote_seq_start: " << remote_seq_start << " remote_seq_end: " << remote_seq_end << endl;
    }

    // called by CTL thread
    // confirmation for client logon
    void OnClientLogon(const struct sockaddr_in& addr, Connection& conn) {
        cout << "Client Logon from: " << inet_ntoa(addr.sin_addr) << ":" << ntohs(addr.sin_port)
             << ", name: " << conn.GetRemoteName() << endl;
    }

    // called by CTL thread
    // client is disconnected
    void OnClientDisconnected(Connection& conn, const char* reason, int sys_errno) {
        cout << "Client disconnected,.name: " << conn.GetRemoteName() << " reason: " << reason
             << " syserrno: " << strerror(sys_errno) << endl;
    }

    // called by APP thread
    void OnClientMsg(Connection& conn, MsgHeader* recv_header) {
        // auto recv_body_size=recv_header->size-sizeof(MsgHeader);
        MsgHeader* send_header=nullptr;
        switch(recv_header->msg_type) {
            case MSG_PING_TYPE: {
                cout << "Got ping request, seq: " << recv_header->GetMsgBodyPtr<PingReq>()->val << endl;
                conn.TrySendMsg<PingResp>([](PingResp* resp) {
                    strcpy(resp->val,"pong");
                },false);
                break;
            }
            case MSG_SUB_TYPE: {
                SubReq req=*recv_header->GetMsgBodyPtr<SubReq>();
                conn.Subscribe(SubID(req.serviceID,req.messageID));
                conn.TrySendMsg<SubResp>([](SubResp* resp) {
                    strcpy(resp->result,"success");
                },false);
                break;
            }
            case MSG_UNSUB_TYPE: {
                UnsubReq req=*recv_header->GetMsgBodyPtr<UnsubReq>();
                conn.Unsubscribe(SubID(req.serviceID,req.messageID));
                break;
            }
            default: {
                cout << "Unknown msg type: " << recv_header->msg_type << endl;
                break;
            }
        }
    }

    static volatile bool stopped;
private:
    const ServerConf& conf_;
};

volatile bool Server::stopped = false;

int main() {
    YAML::Node config = YAML::LoadFile("config.yaml");
    ServerConf server_conf;
    parseServerConf(config["TcpShm"],server_conf);
    parseMdlApiConf(config["MdlApi"],mdl_conf);

    Server server("Myserver", server_conf.ServerName,server_conf);
    server.Run("0.0.0.0", server_conf.ListenPort);
    return 0;
}
